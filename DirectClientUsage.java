package com.example.fileserver.standalone;

import com.xmcares.framework.fsclient.*;
import com.xmcares.framework.fsclient.minio.MinioClient;
import com.xmcares.framework.fsclient.minio.MinioClientFactory;
import com.xmcares.framework.fsclient.minio.MinioFileDesc;
import com.xmcares.framework.fsclient.minio.MinioProperties;
import com.xmcares.framework.fsclient.sftp.SftpClient;
import com.xmcares.framework.fsclient.sftp.SftpClientFactory;
import com.xmcares.framework.fsclient.sftp.SftpProperties;
import com.xmcares.framework.fsclient.ftp.FtpClient;
import com.xmcares.framework.fsclient.ftp.FtpClientFactory;
import com.xmcares.framework.fsclient.ftp.FtpProperties;

import java.io.*;

/**
 * 直接使用客户端工厂的示例（适用于非Spring环境）
 */
public class DirectClientUsage {
    
    /**
     * MinIO客户端使用示例
     */
    public static void minioExample() throws IOException {
        // 配置MinIO属性
        MinioProperties properties = new MinioProperties();
        properties.setEndpoint("http://localhost:9000");
        properties.setAccessKey("minioadmin");
        properties.setSecretKey("minioadmin");
        properties.setDefaultBucket("test-bucket");
        
        // 创建客户端工厂
        MinioClientFactory factory = new MinioClientFactory(properties);
        
        // 使用客户端
        try (MinioClient client = factory.createFSClient()) {
            // 上传文件
            FileDesc fileDesc = MinioFileDesc.builder()
                    .fileName("test.txt")
                    .storeName("/documents/test.txt")
                    .bucket("my-bucket")
                    .contentType("text/plain")
                    .attribute("author", "system")
                    .build();
            
            InputStream inputStream = new ByteArrayInputStream("Hello World".getBytes());
            FileDescResult uploadResult = client.uploadFile(fileDesc, inputStream);
            
            if (uploadResult.isSuccess()) {
                System.out.println("文件上传成功");
                
                // 下载文件
                ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
                FileDescResult downloadResult = client.downloadFile(fileDesc, outputStream);
                
                if (downloadResult.isSuccess()) {
                    System.out.println("文件内容: " + outputStream.toString());
                }
                
                // 删除文件
                client.deleteFile(fileDesc);
                System.out.println("文件删除成功");
            }
        }
    }
    
    /**
     * SFTP客户端使用示例
     */
    public static void sftpExample() throws IOException {
        // 配置SFTP属性
        SftpProperties properties = new SftpProperties();
        properties.setHost("*************");
        properties.setPort(22);
        properties.setUsername("sftpuser");
        properties.setPassword("sftppass");
        
        // 创建客户端工厂
        SftpClientFactory factory = new SftpClientFactory(properties);
        
        // 使用客户端
        try (SftpClient client = factory.createFSClient()) {
            // 上传文件
            FileDesc fileDesc = new FileDesc.FileDescImpl("/remote/path/test.txt");
            InputStream inputStream = new FileInputStream(new File("/local/path/test.txt"));
            
            FileDescResult result = client.uploadFile(fileDesc, inputStream);
            if (result.isSuccess()) {
                System.out.println("SFTP文件上传成功");
            }
        }
        
        // 销毁工厂资源
        factory.destroy();
    }
    
    /**
     * FTP客户端使用示例
     */
    public static void ftpExample() throws IOException {
        // 配置FTP属性
        FtpProperties properties = new FtpProperties();
        properties.setHost("*************");
        properties.setPort(21);
        properties.setUsername("ftpuser");
        properties.setPassword("ftppass");
        properties.setClientMode(2); // PASSIVE模式
        
        // 创建客户端工厂
        FtpClientFactory factory = new FtpClientFactory();
        // 注意：FTP需要额外的连接池配置，这里简化处理
        
        // 使用客户端
        try (FtpClient client = factory.createFSClient()) {
            // 上传文件
            FileDesc fileDesc = new FileDesc.FileDescImpl("/remote/path/test.txt");
            InputStream inputStream = new FileInputStream(new File("/local/path/test.txt"));
            
            FileDescResult result = client.uploadFile(fileDesc, inputStream);
            if (result.isSuccess()) {
                System.out.println("FTP文件上传成功");
            }
        }
    }
    
    /**
     * 使用FSTemplate的示例
     */
    public static void fsTemplateExample() throws Exception {
        // 创建MinIO工厂
        MinioProperties properties = new MinioProperties();
        properties.setEndpoint("http://localhost:9000");
        properties.setAccessKey("minioadmin");
        properties.setSecretKey("minioadmin");
        properties.setDefaultBucket("test-bucket");
        
        MinioClientFactory factory = new MinioClientFactory(properties);
        
        // 创建FSTemplate
        FSTemplate fsTemplate = new FSTemplate(factory);
        
        // 使用FSTemplate进行文件操作
        InputStream inputStream = new ByteArrayInputStream("Hello FSTemplate".getBytes());
        FileDescResult result = fsTemplate.saveFile("/test/template.txt", inputStream);
        
        if (result.isSuccess()) {
            System.out.println("FSTemplate文件上传成功");
            
            // 下载文件
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            fsTemplate.loadFile("/test/template.txt", outputStream);
            System.out.println("文件内容: " + outputStream.toString());
            
            // 删除文件
            fsTemplate.deleteFile("/test/template.txt");
            System.out.println("文件删除成功");
        }
    }
    
    public static void main(String[] args) {
        try {
            System.out.println("=== MinIO示例 ===");
            minioExample();
            
            System.out.println("\n=== FSTemplate示例 ===");
            fsTemplateExample();
            
            // 其他示例需要相应的服务器环境
            // sftpExample();
            // ftpExample();
            
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
