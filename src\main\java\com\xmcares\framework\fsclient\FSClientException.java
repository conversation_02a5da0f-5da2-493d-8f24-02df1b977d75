/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/14
 */
package com.xmcares.framework.fsclient;

import com.xmcares.framework.commons.error.BaseException;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class FSClientException extends BaseException {

    public FSClientException() {
    }

    public FSClientException(String message) {
        super(message);
    }

    public FSClientException(String message, Throwable cause) {
        super(message, cause);
    }

    public FSClientException(Throwable cause) {
        super(cause);
    }

    public FSClientException(String message, Throwable cause, boolean enableSuppression, boolean writableStackTrace) {
        super(message, cause, enableSuppression, writableStackTrace);
    }
}
