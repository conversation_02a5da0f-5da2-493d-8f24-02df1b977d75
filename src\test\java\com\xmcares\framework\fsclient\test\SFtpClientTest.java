/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2021/5/18
 */
package com.xmcares.framework.fsclient.test;

import com.jcraft.jsch.*;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.sftp.SftpClient;
import com.xmcares.framework.fsclient.sftp.SftpClientFactory;
import com.xmcares.framework.fsclient.sftp.SftpProperties;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;

import java.io.*;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
public class SFtpClientTest {

    private Logger logger = LoggerFactory.getLogger(SFtpClientTest.class);

    private String host = "*************";

    private Integer port = 22;

    private final String default_username = "tech";

    private final String default_password = "tech@2023";

    private Integer sessionTimeout = 30000;

    private Integer channelTimeout = 30000;


    public boolean uploadFile(String localFilePath, String remoteFilePath) {
        ChannelSftp channelSftp = createChannelSftp();
        try {
            channelSftp.put(localFilePath, remoteFilePath);
            return true;
        } catch(SftpException ex) {
            logger.error("Error upload file", ex);
        } finally {
            disconnectChannelSftp(channelSftp);
        }

        return false;
    }

    public boolean downloadFile(String localFilePath, String remoteFilePath) {
        ChannelSftp channelSftp = createChannelSftp();
        OutputStream outputStream;
        try {
            File file = new File(localFilePath);
            outputStream = new FileOutputStream(file);
            channelSftp.get(remoteFilePath, outputStream);
            file.createNewFile();
            return true;
        } catch(SftpException | IOException ex) {
            logger.error("Error download file", ex);
        } finally {
            disconnectChannelSftp(channelSftp);
        }

        return false;
    }

    private ChannelSftp createChannelSftp() {
        try {
            JSch jSch = new JSch();
            Session session = jSch.getSession(default_username, host, port);
            session.setConfig("StrictHostKeyChecking", "no");
            session.setPassword(default_password);
            session.connect(sessionTimeout);
            Channel channel = session.openChannel("sftp");
            channel.connect(channelTimeout);
            return (ChannelSftp) channel;
        } catch(JSchException ex) {
            logger.error("Create ChannelSftp error", ex);
        }

        return null;
    }

    private void disconnectChannelSftp(ChannelSftp channelSftp) {
        try {
            if( channelSftp == null)
                return;

            if(channelSftp.isConnected())
                channelSftp.disconnect();

            if(channelSftp.getSession() != null)
                channelSftp.getSession().disconnect();

        } catch(Exception ex) {
            logger.error("SFTP disconnect error", ex);
        }
    }

    public static void main(String[] args) throws Exception {
        //SFtpClientTest client = new SFtpClientTest();
        //client.uploadFile("/Users/<USER>/Downloads/阿黎明.xlsx", "/阿哥.xlsx");

        SftpProperties properties = new SftpProperties();
        properties.setHost("*************");
        properties.setUsername("tech");
        properties.setPassword("tech");
        SftpClientFactory factory = new SftpClientFactory(properties);
        SftpClient fsClient = factory.createFSClient();
        FileDesc fileDesc = new FileDesc.FileDescImpl("/test1/ddd/bbb.xlsx");
        FileInputStream inputStream = new FileInputStream(new File("/Users/<USER>/Downloads/阿黎明.xlsx"));
        fsClient.uploadFile(fileDesc, inputStream);

        System.out.println("=========="+fileDesc);

        fsClient.close();

        factory.destroy();
    }
}
