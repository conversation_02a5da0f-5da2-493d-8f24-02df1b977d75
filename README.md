# XCNF文件服务器客户端 - 使用说明文档

## 📖 文档概述

这是一个完整的HTML格式技术文档，详细介绍了XCNF文件服务器客户端的使用方法、配置指南和最佳实践。

## 🚀 快速开始

### 1. 打开文档
直接在浏览器中打开 `file-server-documentation.html` 文件即可查看完整文档。

### 2. 文档特性
- ✅ **响应式设计** - 支持桌面和移动设备
- ✅ **交互式导航** - 侧边栏快速导航
- ✅ **搜索功能** - 全文搜索，支持关键词高亮
- ✅ **代码复制** - 一键复制代码示例
- ✅ **代码折叠** - 长代码块可折叠展示
- ✅ **语法高亮** - 支持多种编程语言
- ✅ **打印友好** - 优化的打印样式
- ✅ **键盘快捷键** - 支持快捷键操作

## 📋 文档内容

### 核心章节
1. **项目概述** - 框架介绍和架构设计
2. **核心特性** - 主要功能和优势
3. **环境要求** - 系统要求和依赖配置
4. **存储类型** - 6种存储后端详细说明

### 配置指南
- **本地存储配置** - 开发测试环境
- **FTP配置** - 传统文件传输协议
- **SFTP配置** - 安全文件传输协议
- **MinIO配置** - 现代对象存储
- **FastDFS配置** - 分布式文件系统
- **WebService配置** - 企业级集成

### 开发指南
- **Spring Boot集成** - 完整的集成示例
- **API参考文档** - 详细的API说明
- **安全配置** - 安全最佳实践
- **故障排查** - 常见问题解决方案
- **部署指南** - 生产环境部署

## 🎯 使用技巧

### 搜索功能
- 使用顶部搜索框快速查找内容
- 支持中英文关键词搜索
- 搜索结果会自动高亮显示

### 键盘快捷键
- `Ctrl+F` / `Cmd+F` - 聚焦搜索框
- `ESC` - 清除搜索结果
- `Alt+1~9` - 快速导航到对应章节

### 代码操作
- 点击"复制"按钮一键复制代码
- 点击"折叠/展开"切换长代码块显示
- 所有代码示例都有语法高亮

### 移动端使用
- 点击左上角菜单按钮打开/关闭侧边栏
- 支持触摸滑动和缩放
- 自适应屏幕尺寸

## 🛠 技术特性

### 前端技术
- **HTML5** - 现代HTML标准
- **CSS3** - 响应式设计和动画
- **JavaScript** - 交互功能和搜索
- **Prism.js** - 代码语法高亮
- **Font Awesome** - 图标库

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 文件结构
```
├── file-server-documentation.html  # 主文档文件
├── README.md                       # 使用说明
├── application-*.yml               # 配置文件示例
├── *.java                         # Java代码示例
├── docker-compose.yml             # Docker部署配置
├── Dockerfile                     # Docker镜像配置
└── *.md                          # 其他说明文档
```

## 📝 更新日志

### v1.0.0 (2025-01)
- ✅ 完整的HTML文档结构
- ✅ 6种存储类型配置指南
- ✅ Spring Boot集成示例
- ✅ 交互式搜索功能
- ✅ 响应式设计
- ✅ 代码复制和折叠功能
- ✅ 语法高亮支持
- ✅ 移动端适配

## 🤝 贡献指南

如需更新文档内容：
1. 直接编辑 `file-server-documentation.html` 文件
2. 保持HTML结构和CSS样式的一致性
3. 确保所有代码示例的准确性
4. 测试在不同浏览器和设备上的显示效果

## 📞 技术支持

如有问题或建议，请联系技术支持团队。

---

**© 2025 厦门民航凯亚有限公司 - 保留所有权利**
