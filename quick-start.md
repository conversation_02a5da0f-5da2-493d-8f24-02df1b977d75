# 文件服务器快速开始指南

## 1. 环境准备

### 系统要求
- Java 8+
- Maven 3.6+
- Spring Boot 2.x+

### 依赖检查
```bash
# 检查Java版本
java -version

# 检查Maven版本
mvn -version
```

## 2. 快速集成步骤

### 步骤1: 添加依赖
在您的`pom.xml`中添加：
```xml
<dependency>
    <groupId>com.xmcares.framework</groupId>
    <artifactId>xcnf-data-fsclient</artifactId>
    <version>*******-SNAPSHOT</version>
</dependency>
```

### 步骤2: 配置文件
创建`application.yml`：
```yaml
xcnf:
  data:
    fsclient:
      type: local  # 开始使用本地文件系统
      local:
        base-dir: ./files
```

### 步骤3: 创建服务类
```java
@Service
public class QuickFileService {
    
    @Autowired
    private FSTemplate fsTemplate;
    
    public String uploadFile(MultipartFile file) throws Exception {
        String fileName = "/uploads/" + file.getOriginalFilename();
        FileDescResult result = fsTemplate.saveFile(fileName, file.getInputStream());
        return result.isSuccess() ? fileName : null;
    }
    
    public void downloadFile(String fileName, OutputStream output) throws Exception {
        fsTemplate.loadFile(fileName, output);
    }
}
```

### 步骤4: 创建控制器
```java
@RestController
@RequestMapping("/files")
public class QuickFileController {
    
    @Autowired
    private QuickFileService fileService;
    
    @PostMapping("/upload")
    public ResponseEntity<String> upload(@RequestParam("file") MultipartFile file) {
        try {
            String fileName = fileService.uploadFile(file);
            return ResponseEntity.ok(fileName);
        } catch (Exception e) {
            return ResponseEntity.status(500).body("Upload failed: " + e.getMessage());
        }
    }
    
    @GetMapping("/download/{filename}")
    public void download(@PathVariable String filename, HttpServletResponse response) {
        try {
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition", "attachment; filename=" + filename);
            fileService.downloadFile("/uploads/" + filename, response.getOutputStream());
        } catch (Exception e) {
            response.setStatus(404);
        }
    }
}
```

### 步骤5: 启动应用
```java
@SpringBootApplication
public class FileServerApplication {
    public static void main(String[] args) {
        SpringApplication.run(FileServerApplication.class, args);
    }
}
```

## 3. 测试验证

### 上传文件测试
```bash
curl -X POST -F "file=@test.txt" http://localhost:8080/files/upload
```

### 下载文件测试
```bash
curl -O http://localhost:8080/files/download/test.txt
```

## 4. 切换到其他存储类型

### 切换到MinIO
```yaml
xcnf:
  data:
    fsclient:
      type: minio
      minio:
        endpoint: http://localhost:9000
        access-key: minioadmin
        secret-key: minioadmin
        default-bucket: test-bucket
```

### 切换到FTP
```yaml
xcnf:
  data:
    fsclient:
      type: ftp
      ftp:
        host: localhost
        port: 21
        username: ftpuser
        password: ftppass
```

## 5. 常见问题解决

### 问题1: 自动配置不生效
**解决方案**: 确保Spring Boot能扫描到配置类
```java
@SpringBootApplication
@ComponentScan(basePackages = {"com.example", "com.xmcares.framework.fsclient"})
public class Application {
    // ...
}
```

### 问题2: 连接超时
**解决方案**: 增加超时配置
```yaml
xcnf:
  data:
    fsclient:
      ftp:
        connect-timeout: 60000
        default-timeout: 120000
```

### 问题3: 文件上传失败
**解决方案**: 检查目录权限和磁盘空间
```bash
# 检查目录权限
ls -la ./files

# 检查磁盘空间
df -h
```

## 6. 生产环境配置建议

### 安全配置
```yaml
xcnf:
  data:
    fsclient:
      filter:
        file-type: jpg,png,pdf,doc,docx
        max-upload-size: 10485760  # 10MB
      crypto:
        key: "your-production-encryption-key"
```

### 监控配置
```yaml
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics
  endpoint:
    health:
      show-details: always
```

### 日志配置
```yaml
logging:
  level:
    com.xmcares.framework.fsclient: DEBUG
  file:
    name: logs/fileserver.log
```

## 7. 下一步

1. 阅读完整的配置文档
2. 了解安全最佳实践
3. 配置监控和告警
4. 进行性能调优
5. 设置备份策略
