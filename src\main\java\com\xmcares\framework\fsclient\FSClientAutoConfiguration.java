/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/15
 */
package com.xmcares.framework.fsclient;

import com.xmcares.framework.commons.context.ApplicationContextUtils;
import com.xmcares.framework.commons.util.ClassUtils;
import com.xmcares.framework.commons.util.asserts.Asserts;
import com.xmcares.framework.commons.util.string.StringUtils;
import com.xmcares.framework.fsclient.crypto.DefaultCryptoStream;
import com.xmcares.framework.fsclient.fastdfs.FdfsClientFactory;
import com.xmcares.framework.fsclient.filter.FileSizeLimitInterceptor;
import com.xmcares.framework.fsclient.filter.FileTypeInterceptor;
import com.xmcares.framework.fsclient.filter.FileUploadInterceptor;
import com.xmcares.framework.fsclient.ftp.FtpClient;
import com.xmcares.framework.fsclient.ftp.FtpClientFactory;
import com.xmcares.framework.fsclient.ftp.FtpPooledObjectFactory;
import com.xmcares.framework.fsclient.ftp.FtpProperties;
import com.xmcares.framework.fsclient.local.LocalFSClientFactory;
import com.xmcares.framework.fsclient.local.LocalFSProperties;
import com.xmcares.framework.fsclient.minio.MinioClientFactory;
import com.xmcares.framework.fsclient.minio.MinioProperties;
import com.xmcares.framework.fsclient.sftp.SftpClientFactory;
import com.xmcares.framework.fsclient.sftp.SftpProperties;
import com.xmcares.framework.fsclient.xfs.XfsClientFactory;
import com.xmcares.framework.fsclient.xfs.XfsProperties;
import okhttp3.OkHttpClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.pool2.impl.GenericObjectPool;
import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.transport.http.URLConnectionHTTPConduit;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.ObjectProvider;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.ApplicationContext;
import org.springframework.context.EnvironmentAware;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.Profile;
import org.springframework.core.env.Environment;

import java.util.*;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@Configuration
@EnableConfigurationProperties(FSClientProperties.class)
public class FSClientAutoConfiguration implements EnvironmentAware {
    private static final Logger log = LoggerFactory.getLogger(FSClientAutoConfiguration.class);

    //----------------- 公共配置 -----------------//

    @Bean
    public FSTemplate fileRepository(ObjectProvider<FSClientFactory> fsClientFactories, ObjectProvider<FSClientProperties> fsClientPropertiesObjectProvider) {
        if (fsClientFactories.getIfAvailable() == null) {
            throw new IllegalArgumentException("FSClientFactory Bean 不存在");
        }
        FSClientProperties fsClientProperties = fsClientPropertiesObjectProvider.getIfAvailable();
        if (fsClientProperties == null) {
            throw new IllegalArgumentException("FSClientProperties Bean 不存在");
        }
        List<FileUploadInterceptor> fileUploadInterceptors = getFileUploadInterceptor(fsClientProperties);
        FSTemplate fsTemplate = new FSTemplate(fsClientFactories.getIfAvailable());
        fsTemplate.setFsClientProperties(fsClientProperties);
        fsTemplate.setUploadInterceptors(fileUploadInterceptors);
        return fsTemplate;
    }

    private List<FileUploadInterceptor> getFileUploadInterceptor(FSClientProperties properties) {
        List<FileUploadInterceptor> fileUploadInterceptors = new ArrayList<>();
        fileUploadInterceptors.add(new FileSizeLimitInterceptor());
        fileUploadInterceptors.add(new FileTypeInterceptor());
        // 添加自定义的构造起
        List<String> filterClass = properties.getFilter().getFilterClass();
        for (String filterClassName : filterClass) {
            if(StringUtils.hasLength(filterClassName)){
                // SpringBoot 反射的方式生成 类对象 异常捕获掉
                FileUploadInterceptor interceptor = newFileUploadInterceptor(filterClassName);
                if(null != interceptor){
                    fileUploadInterceptors.add(interceptor);
                }
			}
        }

        for (FileUploadInterceptor interceptor : fileUploadInterceptors) {
            interceptor.setFsClientProperties(properties);
        }
        return fileUploadInterceptors;
    }

    private FileUploadInterceptor newFileUploadInterceptor(String filterClassName) {
        FileUploadInterceptor interceptor = null;
        try {
            Class<?> clazz = ClassUtils.forName(filterClassName);
            if (FileUploadInterceptor.class.isAssignableFrom(clazz)) {
                interceptor = (FileUploadInterceptor) BeanUtils.instantiateClass(clazz);
            } else {
                log.debug("Class {} does not implement FileUploadInterceptor", filterClassName);
            }
        } catch (Exception e) {
            log.warn("Can not instantiate FileUploadInterceptor {} : {}", filterClassName, e.getMessage());
        }
        return interceptor;
    }

    @Bean
    public DefaultCryptoStream cryptoStream(FSClientProperties properties) {
        return new DefaultCryptoStream(properties.getCrypto().getKey());
    }

    @Override
    public void setEnvironment(Environment environment) {
        System.out.println(environment);
    }


    //----------------- LOCAL 客户端配置 -----------------//

    @Configuration
    @ConditionalOnProperty(value = "xcnf.data.fsclient.type", matchIfMissing = true, havingValue = "local")
    @ConditionalOnMissingBean(FSClientFactory.class)
    class LocalFSConfiguration {
        @Bean
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.local")
        public LocalFSProperties localFSProperties() {
            return new LocalFSProperties();
        }
        /**
         * 注册本地文件系统客户端工厂
         * @param localFSProperties
         * @return
         */
        @Bean
        public FSClientFactory fdfsClientFactory(LocalFSProperties localFSProperties) {
            LocalFSClientFactory factory = new LocalFSClientFactory(localFSProperties);
            return factory;
        }

    }

    //----------------- FTP 客户端配置 -----------------//

    @Configuration
    @ConditionalOnProperty(value = "xcnf.data.fsclient.type", havingValue = "ftp")
    @ConditionalOnMissingBean(FSClientFactory.class)
    class FtpConfiguration {

        @Bean
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.ftp")
        public FtpProperties ftpProperties() {
            return new FtpProperties();
        }

        /**
         * FTP文件系统客户端池对象工厂
         * @param ftpClientConfig
         * @param ftpProperties
         * @return
         */
        @Bean
        public FtpPooledObjectFactory ftpPooledObjectFactory(ObjectProvider<FTPClientConfig> ftpClientConfig, FtpProperties ftpProperties) {
            FtpPooledObjectFactory pooledObjectFactory = new FtpPooledObjectFactory();
            pooledObjectFactory.setProperties(ftpProperties);
            pooledObjectFactory.setConfig(ftpClientConfig.getIfAvailable());
            return pooledObjectFactory;
        }

        /**
         * FTP文件系统客户端对象池配置，使用Apache commons-pool工具
         * @return
         */
        @Bean @Qualifier("ftpClientPoolConfig")
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.ftp.pool")
        public GenericObjectPoolConfig ftpClientPoolConfig() {
            GenericObjectPoolConfig poolConfig = new GenericObjectPoolConfig();
            poolConfig.setTestOnBorrow(true);
            poolConfig.setTestOnReturn(true);
            poolConfig.setTestWhileIdle(true);
            poolConfig.setMinEvictableIdleTimeMillis(60000);
            poolConfig.setSoftMinEvictableIdleTimeMillis(50000);
            poolConfig.setTimeBetweenEvictionRunsMillis(30000);
            return poolConfig;
        }

        @Bean
        @Qualifier("ftpClientPool")
        public GenericObjectPool<FtpClient> ftpClientPool(GenericObjectPoolConfig ftpClientPoolConfig,
                                                          ObjectProvider<FtpPooledObjectFactory> ftpPooledObjectFactories) {
            return new GenericObjectPool<>(ftpPooledObjectFactories.getIfAvailable(), ftpClientPoolConfig);
        }

        /**
         * 注册FTP的文件系统客户端工厂
         * @param ftpClientPool
         * @return
         */
        @Bean
        @Qualifier("ftpClientFactory")
        public FSClientFactory ftpClientFactory(GenericObjectPool ftpClientPool) {
            FtpClientFactory clientFactory = new FtpClientFactory();
            clientFactory.setClientPool(ftpClientPool);
            return clientFactory;
        }
    }


    //----------------- FastDFS 客户端配置 -----------------//

    @Configuration
    @ConditionalOnProperty(value = "xcnf.data.fsclient.type", havingValue = "fastdfs")
    @ConditionalOnMissingBean(FSClientFactory.class)
    class FdfsConfiguration {
        @Bean
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.fdfs")
        public Properties fdfsProperties() {
            return new Properties();
        }
        /**
         * 注册FastDFS的文件系统客户端工厂
         * @param fdfsProperties
         * @return
         */
        @Bean
        public FSClientFactory fdfsClientFactory(Properties fdfsProperties) {
            FdfsClientFactory clientFactory = new FdfsClientFactory();
            clientFactory.setProperties(toCsourceProperties(fdfsProperties));
            return clientFactory;
        }

        /**
         * 转换成Csource Fasdfs框架内部的参数格式需要
         * @see org.csource.fastdfs.ClientGlobal#PROP_KEY_CHARSET
         * @param properties
         * @return
         */
        private Properties toCsourceProperties(Properties properties) {
            Properties csourceProperties = new Properties();
            Set<Map.Entry<Object, Object>> entries = properties.entrySet();
            for (Map.Entry<Object, Object> entry : properties.entrySet()) {
                csourceProperties.put("fastdfs." + entry.getKey().toString(),  entry.getValue().toString());

            }
            return csourceProperties;
        }
    }


    //----------------- MinIO 客户端配置 -----------------//

    @Configuration
    @ConditionalOnProperty(value = "xcnf.data.fsclient.type", havingValue = "minio")
    @ConditionalOnMissingBean(FSClientFactory.class)
    class MinioConfiguration {
        @Bean
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.minio")
        public MinioProperties minioProperties() {
            return new MinioProperties();
        }
        /**
         * 注册FastDFS的文件系统客户端工厂
         * @param minioProperties
         * @return
         */
        @Bean
        public MinioClientFactory minioClientFactory(MinioProperties minioProperties,
                                                     ObjectProvider<OkHttpClient> okHttpClientObjectProvider,
                                                     @Value("${spring.application.name:default}")String globalDefaultBucket) {
            OkHttpClient okHttpClient = okHttpClientObjectProvider.getIfAvailable();
            MinioClientFactory clientFactory = new MinioClientFactory(minioProperties, okHttpClient);
            //如果没有配置defaultBucket，则使用全局默认bucket
            minioProperties.setDefaultBucketIfAbsent(globalDefaultBucket);
            return clientFactory;
        }
    }



    //----------------- 研发组webservice文件服务器客户端配置 -----------------//

    @Configuration
    @ConditionalOnProperty(value = "xcnf.data.fsclient.type", havingValue = "xfs")
    @ConditionalOnMissingBean(FSClientFactory.class)
    class XfsConfiguration {

        @Bean
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.xfs")
        public XfsProperties xfsProperties() {
            return new XfsProperties();
        }

        @Bean
        public FSClientFactory xfsClientFactory(XfsProperties xfsProperties) {
            JaxWsDynamicClientFactory wsClientFactory = JaxWsDynamicClientFactory.newInstance();
            Client wsClient = wsClientFactory.createClient(xfsProperties.getWsdlUrl());
            if (xfsProperties.getHttpPolicy() != null) {
                URLConnectionHTTPConduit conduit = (URLConnectionHTTPConduit)wsClient.getConduit();
                conduit.setClient(xfsProperties.getHttpPolicy());
            }
            XfsClientFactory clientFactory = new XfsClientFactory();
            clientFactory.setWsClient(wsClient);
            return clientFactory;
        }
    }



    //----------------- SFTP文件服务器客户端配置 -----------------//

    @Configuration
    @ConditionalOnProperty(value = "xcnf.data.fsclient.type", havingValue = "sftp")
    @ConditionalOnMissingBean(FSClientFactory.class)
    class SftpConfiguration {

        @Bean
        @ConfigurationProperties(prefix = "xcnf.data.fsclient.sftp")
        public SftpProperties sftpProperties() {
            SftpProperties properties = new SftpProperties();
            //TODO 改进配置项
            return properties;
        }

        @Bean
        public FSClientFactory sftpClientFactory(SftpProperties sftpProperties) {
            return new SftpClientFactory(sftpProperties);
        }

    }


}
