# FastDFS分布式文件系统配置
xcnf:
  data:
    fsclient:
      type: fastdfs
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # FastDFS具体配置
      fdfs:
        tracker_servers: 192.168.1.100:22122,192.168.1.101:22122
        connect_timeout_in_seconds: 5
        network_timeout_in_seconds: 30
        charset: UTF-8
        http_anti_steal_token: false
        http_secret_key: FastDFS1234567890
        http_tracker_http_port: 80
