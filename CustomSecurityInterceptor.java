package com.example.fileserver.config;

import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import com.xmcares.framework.fsclient.filter.FileUploadInterceptor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.util.Arrays;
import java.util.List;

/**
 * 自定义安全拦截器
 */
public class CustomSecurityInterceptor implements FileUploadInterceptor {
    
    private static final Logger logger = LoggerFactory.getLogger(CustomSecurityInterceptor.class);
    
    // 危险文件扩展名黑名单
    private static final List<String> DANGEROUS_EXTENSIONS = Arrays.asList(
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "sh"
    );
    
    // 危险文件头部特征
    private static final byte[][] DANGEROUS_HEADERS = {
        {0x4D, 0x5A},  // PE executable (MZ)
        {0x50, 0x4B},  // ZIP/JAR files (PK)
        // 可以添加更多危险文件头部特征
    };
    
    @Override
    public FileDescResult preUpload(FileDesc fileDesc, InputStream inputStream) {
        try {
            // 1. 检查文件扩展名
            String fileName = fileDesc.getFileName();
            if (fileName != null) {
                String extension = getFileExtension(fileName).toLowerCase();
                if (DANGEROUS_EXTENSIONS.contains(extension)) {
                    logger.warn("拒绝上传危险文件类型: {}", fileName);
                    return FileDescResult.builder()
                            .success(false)
                            .fileDesc(fileDesc)
                            .errorCode("DANGEROUS_FILE_TYPE")
                            .errorMessage("不允许上传此类型的文件: " + extension)
                            .build();
                }
            }
            
            // 2. 检查文件头部特征
            if (inputStream.markSupported()) {
                inputStream.mark(8); // 标记前8个字节
                byte[] header = new byte[8];
                int bytesRead = inputStream.read(header);
                inputStream.reset(); // 重置流位置
                
                if (bytesRead >= 2) {
                    for (byte[] dangerousHeader : DANGEROUS_HEADERS) {
                        if (matchesHeader(header, dangerousHeader)) {
                            logger.warn("检测到危险文件头部特征: {}", fileName);
                            return FileDescResult.builder()
                                    .success(false)
                                    .fileDesc(fileDesc)
                                    .errorCode("DANGEROUS_FILE_HEADER")
                                    .errorMessage("文件内容不符合安全要求")
                                    .build();
                        }
                    }
                }
            }
            
            // 3. 检查文件路径安全性
            String storeName = fileDesc.getStoreName();
            if (storeName != null && (storeName.contains("../") || storeName.contains("..\\") || 
                                     storeName.startsWith("/") && storeName.contains("etc") ||
                                     storeName.contains("system32"))) {
                logger.warn("检测到不安全的文件路径: {}", storeName);
                return FileDescResult.builder()
                        .success(false)
                        .fileDesc(fileDesc)
                        .errorCode("UNSAFE_PATH")
                        .errorMessage("文件路径不安全")
                        .build();
            }
            
            // 4. 记录上传日志
            logger.info("文件安全检查通过: {} -> {}", fileName, storeName);
            
            return FileDescResult.builder()
                    .success(true)
                    .fileDesc(fileDesc)
                    .build();
                    
        } catch (IOException e) {
            logger.error("文件安全检查异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .fileDesc(fileDesc)
                    .errorCode("SECURITY_CHECK_ERROR")
                    .errorMessage("安全检查失败: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 获取文件扩展名
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
    
    /**
     * 检查文件头部是否匹配危险特征
     */
    private boolean matchesHeader(byte[] fileHeader, byte[] dangerousHeader) {
        if (fileHeader.length < dangerousHeader.length) {
            return false;
        }
        
        for (int i = 0; i < dangerousHeader.length; i++) {
            if (fileHeader[i] != dangerousHeader[i]) {
                return false;
            }
        }
        return true;
    }
}
