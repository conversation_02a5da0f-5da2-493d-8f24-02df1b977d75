/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/10/28
 */
package com.xmcares.framework.fsclient.xfs;

import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import org.apache.commons.lang3.StringUtils;
import org.apache.cxf.endpoint.Client;
import org.apache.cxf.service.model.BindingOperationInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.namespace.QName;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

/**
 * xmcares file-system client based on web service
 * @updateBy liucx  obtainParams组装路径的时候不允许filePath为空，
 * <AUTHOR>
 * @since 1.0.0
 */
public class XfsClient implements FSClient {
    protected static final Logger logger = LoggerFactory.getLogger(XfsClient.class);
    public static final String WS_METHOD = "method";
    public static final String WS_CATALOG = "catalog";
    public static final String WS_NAMESPACE = "http://service.filesystem.ws.xmcares.com/";

    private static final String WS_UPLOAD_METHOD = "upload";
    private static final String WS_UPLOAD_ENCRYPT_METHOD = "uploadEncryptFile";
    private static final String WS_DOWNLOAD_METHOD = "download";
    private static final String WS_DOWNLOAD_ENCRYPT_METHOD = "downloadEncryptFile";
    private static final String WS_DELETE_METHOD = "delete";

    private Client wsClient;

    public XfsClient(Client wsClient) {
        this.wsClient = wsClient;
    }

    @Override
    public FileDescResult uploadFile(FileDesc fileDesc, InputStream inputStream) throws IOException {
        BindingOperationInfo operation = this.obtainOperation(fileDesc, WS_UPLOAD_METHOD);
        List<Object> params = this.obtainParams(fileDesc);

        //upload和uploadEncryptFile服务方法需要带上文件类别参数
        if (StringUtils.equals(WS_UPLOAD_METHOD, operation.getName().getLocalPart())
                || StringUtils.equals(WS_UPLOAD_ENCRYPT_METHOD, operation.getName().getLocalPart()) ) {
            params.add(0, this.obtainWsCatalog(fileDesc, "uploadDefault"));
        }

        if (inputStream.available() <= 0) {
            logger.debug("Input stream is empty, Skipping upload");
        }
        //添加文件字节数组参数
        byte[] bytes = new byte[inputStream.available()];
        while (inputStream.read(bytes) > 0){
            params.add(bytes);
            try {
                Object[] result = this.wsClient.invoke(operation, params.toArray());
                if (logger.isInfoEnabled()) {
                    logger.info("Webservice文件服务器上传文件[{}]成功: {}", fileDesc.getFileName(), Arrays.toString(result));
                }
            } catch (Exception e) {
                throw new FSClientException("Webservice文件服务器上传文件失败", e);
            }
        }
        return null;
    }

    @Override
    public FileDescResult downloadFile(FileDesc fileDesc, OutputStream outputStream) throws IOException {
        BindingOperationInfo operation = this.obtainOperation(fileDesc, WS_DOWNLOAD_METHOD);
        List<Object> params = this.obtainParams(fileDesc);

        //download服务方法需要带上文件类别参数
        if (StringUtils.equals(WS_DOWNLOAD_METHOD, operation.getName().getLocalPart())
                || StringUtils.equals(WS_DOWNLOAD_ENCRYPT_METHOD, operation.getName().getLocalPart()) ) {
            params.add(0, this.obtainWsCatalog(fileDesc, "downLoadDefault"));
        }
        Object[] result;
        try {
            result = this.wsClient.invoke(operation, params.toArray());
        } catch (Exception e) {
            throw new FSClientException("Webservice文件服务器下载文件失败", e);
        }
        if (result != null && result.length > 0) {
            outputStream.write((byte[]) result[0]);
        }
        return null;
    }

    @Override
    public void deleteFile(FileDesc fileDesc) throws IOException {
        BindingOperationInfo operation = this.obtainOperation(fileDesc, WS_DELETE_METHOD);
        List<Object> params = this.obtainParams(fileDesc);

        //delete服务方法需要带上文件类别参数
        if (StringUtils.equalsIgnoreCase(WS_DELETE_METHOD, operation.getName().getLocalPart())) {
            //注意：没有deleteDefault
            params.add(0, this.obtainWsCatalog(fileDesc, "uploadDefault"));
        }
        Object[] result;
        try {
            result = this.wsClient.invoke(operation, params.toArray());
            if (logger.isInfoEnabled()) {
                logger.info("Webservice文件服务器删除文件[{}]成功: {}", fileDesc.getFileName(), Arrays.toString(result));
            }
        } catch (Exception e) {
            throw new FSClientException("Webservice文件服务器删除文件失败", e);
        }
    }

    @Override
    public boolean isOpen() {
        return true;
    }

    @Override
    public Object getRawClient() {
        return this.wsClient;
    }

    @Override
    public void close() throws IOException {
        //void
    }

    private List<Object> obtainParams(FileDesc fileDesc) {
        List<Object> params = new ArrayList<>();
        String[] temp = StringUtils.split(fileDesc.getFileName(), "/");
        String fileName = temp[temp.length-1];
        // updateBy liucx 根据服务端代码得 filePath不能为null,否则服务端会报错
        String filePath = "/";
        if (temp.length > 1) {
            filePath = "/"+StringUtils.join(temp, "/", 0, temp.length - 1);
        }
        params.add(filePath);
        params.add(fileName);
        return params;
    }

    private BindingOperationInfo obtainOperation(FileDesc fileDesc, String defaultMethod) {
        //用户指定使用文件服务器WS内置提供的方法：uploadXxx(filePath, fileName, bytes) 或 downLoadXxx(filePath, fileName)
        String method = fileDesc.getAttribute(WS_METHOD);
        if (StringUtils.isEmpty(method)) {
            //upload，download，delete
            method = defaultMethod;
        }
        BindingOperationInfo operation = this.wsClient.getEndpoint().getEndpointInfo().getBinding()
                .getOperation(new QName(WS_NAMESPACE, method));
        if (operation == null) {
            throw new FSClientException("获取不到webservice文件服务方法["+method+"]:"+fileDesc.toString());
        }
        if (operation.isUnwrappedCapable()) {
            operation = operation.getUnwrappedOperation();
        }
        return operation;
    }


    private String obtainWsCatalog(FileDesc fileDesc, String defaultValue) {
        String catalog = fileDesc.getAttribute(WS_CATALOG);
        if (StringUtils.isEmpty(catalog)) {
            catalog = defaultValue;
            if (logger.isWarnEnabled()) {
                logger.warn("Webservice文件服务器方法类别参数值为空，使用默认值:{}", catalog);
            }
        }
        return catalog;
    }

}
