/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/7
 */
package com.xmcares.framework.fsclient.test;

import com.xmcares.framework.fsclient.minio.MinioClient;
import com.xmcares.framework.fsclient.minio.MinioClientFactory;
import com.xmcares.framework.fsclient.minio.MinioFileDesc;
import com.xmcares.framework.fsclient.minio.MinioProperties;
import org.junit.jupiter.api.Test;

import java.io.*;
import java.nio.file.Files;
import java.nio.file.LinkOption;
import java.nio.file.Paths;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
public class MinioClientTest {


    @Test
    public void test_regular_file() {
        System.out.println(Files.isRegularFile(Paths.get("Users/eliming/Downloads/filename"), new LinkOption[0]));


    }

    public static void main(String[] args) throws IOException {
        MinioProperties properties = new MinioProperties();
        properties.setEndpoint("http://192.168.73.172:9000");
        properties.setAccessKey("cux3vJxETu9HGebHBLTi");
        properties.setSecretKey("Lp1Vl4CX2eGQGBR7PYQTQaibiTw5TMHQ1oyMr5OJ");
        properties.setDefaultBucket("bucket01");
        MinioClientFactory clientFactory = new MinioClientFactory(properties);

        MinioClient fsClient = clientFactory.createFSClient();

        for (int i = 0; i < 10; i++) {
            long s = System.currentTimeMillis();
            InputStream input = new FileInputStream(new File("F:/02.BigSoft/XiaZai/minio/test/config.sample.json"));
//            MinioFileDesc fileDesc = new MinioFileDesc("/dir1/test"+i+".json");
//            fileDesc.putAttribute("x", "["+ i +"]");
//            fileDesc.putAttribute("y", "<"+ i +">");
//            fileDesc.putAttribute("content-type", "application/json");
//            fsClient.uploadFile(fileDesc, input);
            long e = System.currentTimeMillis();
            System.out.println("cost: "+ (e-s));
        }

//        MinioFileDesc fileDesc = new MinioFileDesc("/dir1/test1.json");
        System.out.printf("=================");

        OutputStream output = new FileOutputStream(new File("F:/02.BigSoft/XiaZai/minio/test/config.sample2.json"));
//        fsClient.downloadFile(fileDesc, output);

//        fsClient.deleteFile(fileDesc);

    }
}
