package com.xmcares.framework.fsclient.filter;

import com.xmcares.framework.fsclient.FSClientProperties;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;

import java.io.InputStream;

public interface FileUploadInterceptor {

	FileDescResult preUpload(FileDesc fileDesc, InputStream inputStream);

	void setFsClientProperties(FSClientProperties fsClientProperties);

}
