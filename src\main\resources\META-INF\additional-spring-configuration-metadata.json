{"properties": [{"name": "xcnf.data.fsclient.type", "type": "com.xmcares.framework.fsclient.FSType", "sourceType": "com.xmcares.framework.fsclient.FSType", "defaultValue": "LOCAL", "description": "文件服务类型."}, {"name": "xcnf.data.fsclient.crypto.key", "type": "java.lang.String", "description": "文件加密key字符串."}, {"name": "xcnf.data.fsclient.fdfs.tracker_servers", "type": "java.lang.String", "description": "FastDFS tracker servers."}, {"name": "xcnf.data.fsclient.fdfs.http_tracker_http_port", "type": "java.lang.Integer", "description": "FastDFS tracker http port."}, {"name": "xcnf.data.fsclient.fdfs.connect_timeout_in_seconds", "type": "java.lang.Integer", "description": "FastDFS connect timeout."}, {"name": "xcnf.data.fsclient.fdfs.network_timeout_in_seconds", "type": "java.lang.Integer", "description": "FastDFS connect timeout."}, {"name": "xcnf.data.fsclient.fdfs.charset", "type": "java.lang.String", "description": "FastDFS charset."}, {"name": "xcnf.data.fsclient.fdfs.http_secret_key", "type": "java.lang.String", "description": "FastDFS http secret key."}]}