package com.example.fileserver.controller;

import com.example.fileserver.service.FileService;
import com.xmcares.framework.fsclient.FileDescResult;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件操作控制器
 */
@RestController
@RequestMapping("/api/files")
public class FileController {
    
    @Autowired
    private FileService fileService;
    
    /**
     * 上传文件
     */
    @PostMapping("/upload")
    public ResponseEntity<Map<String, Object>> uploadFile(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "path", defaultValue = "/uploads") String targetPath) {
        
        Map<String, Object> response = new HashMap<>();
        
        if (file.isEmpty()) {
            response.put("success", false);
            response.put("message", "文件不能为空");
            return ResponseEntity.badRequest().body(response);
        }
        
        FileDescResult result = fileService.uploadFile(file, targetPath);
        
        if (result != null && result.isSuccess()) {
            response.put("success", true);
            response.put("message", "文件上传成功");
            response.put("fileName", file.getOriginalFilename());
            response.put("size", file.getSize());
            if (result.getFileDesc() != null) {
                response.put("storePath", result.getFileDesc().getStoreName());
            }
        } else {
            response.put("success", false);
            response.put("message", result != null ? result.getErrorMessage() : "上传失败");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 上传文件到指定bucket（MinIO专用）
     */
    @PostMapping("/upload/minio")
    public ResponseEntity<Map<String, Object>> uploadToMinio(
            @RequestParam("file") MultipartFile file,
            @RequestParam(value = "bucket", defaultValue = "default") String bucket,
            @RequestParam("objectName") String objectName) {
        
        Map<String, Object> response = new HashMap<>();
        
        if (file.isEmpty()) {
            response.put("success", false);
            response.put("message", "文件不能为空");
            return ResponseEntity.badRequest().body(response);
        }
        
        FileDescResult result = fileService.uploadFileWithDesc(file, bucket, objectName);
        
        if (result != null && result.isSuccess()) {
            response.put("success", true);
            response.put("message", "文件上传成功");
            response.put("bucket", bucket);
            response.put("objectName", objectName);
        } else {
            response.put("success", false);
            response.put("message", result != null ? result.getErrorMessage() : "上传失败");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 下载文件
     */
    @GetMapping("/download")
    public void downloadFile(
            @RequestParam("path") String filePath,
            @RequestParam(value = "filename", required = false) String downloadFilename,
            HttpServletResponse response) throws IOException {
        
        ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
        FileDescResult result = fileService.downloadFile(filePath, outputStream);
        
        if (result != null && result.isSuccess()) {
            // 设置响应头
            response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
            
            String filename = downloadFilename != null ? downloadFilename : 
                             filePath.substring(filePath.lastIndexOf("/") + 1);
            String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
            
            response.setHeader(HttpHeaders.CONTENT_DISPOSITION, 
                             "attachment; filename=\"" + encodedFilename + "\"");
            
            byte[] fileBytes = outputStream.toByteArray();
            response.setContentLength(fileBytes.length);
            
            // 写入响应
            response.getOutputStream().write(fileBytes);
            response.getOutputStream().flush();
        } else {
            response.setStatus(HttpStatus.NOT_FOUND.value());
            response.getWriter().write("文件不存在或下载失败");
        }
    }
    
    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public ResponseEntity<Map<String, Object>> deleteFile(@RequestParam("path") String filePath) {
        Map<String, Object> response = new HashMap<>();
        
        boolean success = fileService.deleteFile(filePath);
        
        if (success) {
            response.put("success", true);
            response.put("message", "文件删除成功");
        } else {
            response.put("success", false);
            response.put("message", "文件删除失败");
        }
        
        return ResponseEntity.ok(response);
    }
    
    /**
     * 获取文件服务器信息
     */
    @GetMapping("/info")
    public ResponseEntity<Map<String, Object>> getServerInfo() {
        Map<String, Object> response = new HashMap<>();
        
        Object rawClient = fileService.getRawClient();
        if (rawClient != null) {
            response.put("success", true);
            response.put("clientType", rawClient.getClass().getSimpleName());
            response.put("message", "文件服务器连接正常");
        } else {
            response.put("success", false);
            response.put("message", "文件服务器连接异常");
        }
        
        return ResponseEntity.ok(response);
    }
}
