/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2021/5/18
 */
package com.xmcares.framework.fsclient.sftp;

import com.jcraft.jsch.*;
import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.time.Duration;

/**
 * SFTP 客户端实现
 * <AUTHOR>
 * @since 1.1.0
 */
public class SftpClient implements FSClient {
    protected static final Logger logger = LoggerFactory.getLogger(SftpClient.class);

    private static final Duration DEFAULT_CHANNEL_CONNECT_TIMEOUT = Duration.ofSeconds(5);

    private final Session jschSession;
    private ChannelSftp jschChanel;

    private boolean closed;

    private int channelConnectTimeout = (int) DEFAULT_CHANNEL_CONNECT_TIMEOUT.toMillis();

    public SftpClient(Session jschSession) {
        this.jschSession = jschSession;
    }


    @Override
    public FileDescResult uploadFile(FileDesc fileDesc, InputStream inputStream) throws IOException {
        checkState();
        if (fileDesc.getAttributes() != null && !fileDesc.getAttributes().isEmpty()) {
            logger.warn("SFTP 文件服务不支持添加额外Attributes(已忽略掉)");
        }

        String[] dirs = fileDesc.resolveFileDirs();
        try {
            StringBuilder sb = new StringBuilder();
            for (int i = 0, l = dirs.length; i < l; i++) {
                sb.append("/").append(dirs[i]);
                if (!existDir(sb.toString())) {
                    jschChanel.mkdir(sb.toString());
                }
            }
            jschChanel.put(inputStream, fileDesc.getFileName());
        } catch (SftpException e) {
            throw new FSClientException("sftp上传文件错误", e);
        }
        return null;
    }

    @Override
    public FileDescResult downloadFile(FileDesc fileDesc, OutputStream outputStream) throws IOException {
        checkState();
        try {
            this.jschChanel.get(fileDesc.getStoreName(), outputStream);
        } catch (SftpException e) {
            throw new FSClientException("sftp下载文件错误", e);
        }
        return null;
    }

    @Override
    public void deleteFile(FileDesc fileDesc) throws IOException {
        checkState();
        try {
            this.jschChanel.rm(fileDesc.getStoreName());
        } catch (SftpException e) {
            throw new FSClientException("Sftp删除文件失败", e);
        }
    }

    @Override
    public boolean isOpen() {
        return !this.closed && this.jschChanel != null && this.jschChanel.isConnected();
    }

    @Override
    public Object getRawClient() {
        return this.jschChanel;
    }

    @Override
    public void close() throws IOException {
        this.closed = true;
        if (this.jschChanel != null) {
            this.jschChanel.disconnect();
        }
    }

    public void setChannelConnectTimeout(int channelConnectTimeout) {
        this.channelConnectTimeout = channelConnectTimeout;
    }


    private boolean existDir(String dir) {
        try {
            SftpATTRS lstat = jschChanel.lstat(dir);
            return lstat.isDir();
        } catch (SftpException e) {
            //do nothing
        }
        return false;
    }


    void connect() {
        try {
            if (!this.jschSession.isConnected()) {
                this.jschSession.connect();
            }
            this.jschChanel = (ChannelSftp) this.jschSession.openChannel("sftp");
            if (this.jschChanel != null && !this.jschChanel.isConnected()) {
                this.jschChanel.connect(this.channelConnectTimeout);
            }
            this.closed = false;
        } catch (JSchException e) {
            try {
                this.close();
            } catch (IOException ex) {
                //do nothing
            }
            throw new FSClientException("failed to connect", e);
        }
    }
}
