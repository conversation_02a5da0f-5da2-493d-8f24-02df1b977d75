/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/8
 */
package com.xmcares.framework.fsclient.local;

import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.ByteBuffer;
import java.nio.channels.FileChannel;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardOpenOption;

/**
 * 本地文件系统客户端
 * <AUTHOR>
 * @since 1.1.0
 */
public class LocalFSClient implements FSClient {

    protected Logger logger = LoggerFactory.getLogger(this.getClass());

    private String baseDir;

    public LocalFSClient() {
        this.baseDir = System.getProperty("user.home");
    }

    @Override
    public FileDescResult uploadFile(FileDesc fileDesc, InputStream inputStream)  throws IOException{
        if (fileDesc.getAttributes() != null && !fileDesc.getAttributes().isEmpty()) {
            logger.warn("本地文件系统服务不支持添加额外Attributes(已忽略掉)");
        }
        Path path = Paths.get(getFilePath(fileDesc));
        if (!Files.exists(path.getParent())) {
            Files.createDirectories(path.getParent());
        }
        try (FileChannel channel = FileChannel.open(path, StandardOpenOption.CREATE,
                StandardOpenOption.WRITE, StandardOpenOption.TRUNCATE_EXISTING)
        ) {
            byte[] data = new byte[1024];
            ByteBuffer buf;
            int c;
            while ( (c = inputStream.read(data)) != -1) {
                buf = ByteBuffer.wrap(data, 0, c);
                channel.write(buf);
            }
            channel.force(false);
        }
        return null;
    }

    @Override
    public FileDescResult downloadFile(FileDesc fileDesc, OutputStream outputStream) throws IOException {
        try (FileChannel channel = FileChannel.open(Paths.get(getFilePath(fileDesc)), StandardOpenOption.READ)
        ) {
            ByteBuffer buf = ByteBuffer.allocate(1024);
            while (channel.read(buf) != -1) {
                buf.flip();
                while (buf.hasRemaining()){
                    outputStream.write(buf.get());
                }
                buf.clear();
            }
        }
        return null;
    }

    @Override
    public void deleteFile(FileDesc fileDesc) throws IOException {
        Files.delete(Paths.get(getFilePath(fileDesc)));
    }

    @Override
    public boolean isOpen() {
        return true;
    }

    @Override
    public Object getRawClient() {
        return this;
    }

    @Override
    public void close() throws IOException {
    }

    public void setBaseDir(String baseDir) {
        this.baseDir = baseDir;
    }

    private String getFilePath(FileDesc fileDesc) {
        String absPath = fileDesc.getStoreName();
        if (null == absPath || absPath.trim().length() == 0) {
            throw new IllegalArgumentException("fileDesc.storePath不能为空");
        }
        StringBuilder sb = new StringBuilder(this.baseDir);
        if (!absPath.startsWith("/")) {
            sb.append("/");
        }
        sb.append(absPath);
        return sb.toString();
    }
}
