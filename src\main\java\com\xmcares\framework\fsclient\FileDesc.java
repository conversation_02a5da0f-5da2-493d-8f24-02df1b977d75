/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/13
 */
package com.xmcares.framework.fsclient;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import org.apache.commons.lang3.StringUtils;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件描述
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FileDesc {

    public static final String ATTR_BUCKET = "minio.bucket";
    public static final String ATTR_CONTENT_TYPE = "minio.content-type";
    public static final String ATTR_REGION = "minio.region";
    public static final String ATTR_TAG_PREFIX = "tag.";
    public static final String ATTR_METADATA_PREFIX = "metadata.";

    /**
     * 获取文件名
     * @return
     */
    String getFileName();

    /**
     * 设置文件名
     * @param fileName
     */
    void setFileName(String fileName);

    /**
     * 获取文件存盘名
     * @return
     */
    String getStoreName();

    /**
     * 设置文件存储名
     * @param storeName
     */
    void setStoreName(String storeName);

    /**
     * 获取其他信息
     * @return
     */
    Map<String, String> getAttributes();


    void putAttributes(Map<String, String> attributes);

    /**
     * 获取某个信息
     * @param attrName
     * @return
     */
    String getAttribute(String attrName);

    /**
     * 添加信息
     * @param name
     * @param value
     */
    void putAttribute(String name, String value);

    /**
     * 获取文件目录
     * @return
     */
    default String[] resolveFileDirs() {
        String[] dirs = StringUtils.split(this.getFileName(), "/");
        if (dirs == null || dirs.length == 0) {
            return new String[0];
        }
        String[] result = new String[dirs.length-1];
        System.arraycopy(dirs, 0, result, 0, dirs.length-1);
        return result;
    }

    /**
     * 创建默认文件描述
     * @param fileName
     * @return
     */
    static FileDescImpl defaultFileDesc(String fileName) {
        return new FileDescImpl(fileName);
    }

    /**
     * 创建默认文件描述
     * @param fileName
     * @param storeName
     * @return
     */
    static FileDescImpl defaultFileDesc(String fileName, String storeName) {
        return new FileDescImpl(fileName, storeName);
    }

    // 通用的 Builder 接口
    interface Builder<T extends FileDesc> {
        Builder<T> fileName(String fileName);
        Builder<T> storeName(String storeName);
        Builder<T> attribute(String name, String value);
        Builder<T> attributes(Map<String, String> attributes);
        T build();
    }
    
    class FileDescResult {}



    class FileDescImpl implements FileDesc{

        private String fileName = "";

        private String storeName = "";

        private Map<String, String> attributes = new HashMap<>();

        public FileDescImpl(String fileName) {
            this.fileName = fileName;
        }

        public FileDescImpl(String fileName, String storeName) {
            this.fileName = fileName;
            this.storeName = storeName;
        }

        public FileDescImpl() {
        }


        @Override
        public String getFileName() {
            return fileName == null ? storeName : fileName;
        }

        @Override
        public void setFileName(String fileName) {
            this.fileName = fileName;
        }

        @Override
        public String getStoreName() {
            return storeName == null || storeName.equals("") ? this.fileName : this.storeName;
        }

        @Override
        public void setStoreName(String storeName) {
            this.storeName = storeName;
        }

        @Override
        public Map<String, String> getAttributes() {
            return attributes;
        }

        @Override
        public String getAttribute(String attrName) {
            return this.attributes.get(attrName);
        }

        @Override
        public void putAttributes(Map<String, String> attributes) {
            if (attributes != null) {
                this.attributes.putAll(attributes);
            }
        }

        @Override
        public void putAttribute(String name, String value) {
            this.attributes.put(name, value);
        }


        @Override
        public String toString() {
            String attris = null;
            try {
                attris = JacksonJsonUtils.writeObjectAsString(this.attributes);
            } catch (IOException e) {
                //一般不会异常，异常显示成错误即可
                attris = "[serialize error]";
            }
            return "FileDesc{" +
                    "fileName='" + fileName + '\'' +
                    ", storeName='" + storeName + '\'' +
                    ", attributes=" + attris +
                    '}';
        }
    }
}
