/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2021/5/19
 */
package com.xmcares.framework.fsclient.sftp;

import com.jcraft.jsch.Session;

import java.util.concurrent.atomic.AtomicInteger;

/**
 * 包装jsch session。jsch session 可创建多个channel。
 * 使用计数器，保障在所有channel关闭后{@link #close()}，关闭session，释放资源
 * <AUTHOR>
 * @since 1.1.0
 */
public class SftpSession {

    private Session session;

    private final AtomicInteger channels = new AtomicInteger();

    SftpSession(Session session) {
        this.session = session;
    }

    public void addChannel() {
        this.channels.incrementAndGet();
    }

    public void close() {
        if (this.channels.decrementAndGet() <= 0) {
            this.session.disconnect();
        }
    }

    public final Session getSession() {
        return this.session;
    }

    public boolean isConnected() {
        return this.session.isConnected();
    }
}
