package com.xmcares.framework.fsclient.ftp;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.framework.fsclient.FileDesc;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class FtpFileDesc implements FileDesc {
	
	private String fileName = "";
	private String storeName = "";
	private Map<String, String> attributes = new HashMap<>();

	private FtpFileDesc() {}

	private FtpFileDesc(String fileName, String storeName, Map<String, String> attributes) {
		this.fileName = fileName;
		this.storeName = storeName;
		this.attributes = attributes != null ? new HashMap<>(attributes) : new HashMap<>();
	}

	@Override public String getFileName() { return fileName == null ? storeName : fileName; }
	@Override public void setFileName(String fileName) { this.fileName = fileName; }
	@Override public String getStoreName() { return storeName == null || storeName.isEmpty() ? fileName : storeName; }
	@Override public void setStoreName(String storeName) { this.storeName = storeName; }
	@Override public Map<String, String> getAttributes() { return attributes; }
	@Override public String getAttribute(String attrName) { return attributes.get(attrName); }
	@Override public void putAttributes(Map<String, String> attributes) { if (attributes != null) this.attributes.putAll(attributes); }
	@Override public void putAttribute(String name, String value) { attributes.put(name, value); }

	@Override
	public String toString() {
		String attris;
		try {
			attris = JacksonJsonUtils.writeObjectAsString(this.attributes);
		} catch (IOException e) {
			attris = "[serialize error]";
		}
		return "FtpFileDesc{" +
				"fileName='" + fileName + '\'' +
				", storeName='" + storeName + '\'' +
				", attributes=" + attris +
				'}';
	}

	public static FtpFileDescBuilder builder() {
		return new FtpFileDescBuilder();
	}

	/**
	 * FTP Builder
	 */
	public static class FtpFileDescBuilder implements FileDesc.Builder<FtpFileDesc> {
		private String fileName = "";
		private String storeName = "";
		private final Map<String, String> attributes = new HashMap<>();

		@Override
		public FtpFileDescBuilder fileName(String fileName) {
			this.fileName = fileName;
			return this;
		}

		@Override
		public FtpFileDescBuilder storeName(String storeName) {
			this.storeName = storeName;
			return this;
		}

		@Override
		public FtpFileDescBuilder attribute(String name, String value) {
			attributes.put(name, value);
			return this;
		}

		@Override
		public FtpFileDescBuilder attributes(Map<String, String> attributes) {
			if (attributes != null) {
				this.attributes.putAll(attributes);
			}
			return this;
		}

		@Override
		public FtpFileDesc build() {
			return new FtpFileDesc(fileName, storeName, attributes);
		}
	}
}
