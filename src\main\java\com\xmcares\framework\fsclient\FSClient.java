/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/14
 */
package com.xmcares.framework.fsclient;

import java.io.Closeable;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FSClient<T extends FileDesc> extends Closeable {


    FileDescResult uploadFile(T fileDesc, InputStream inputStream) throws IOException;

    FileDescResult downloadFile(T fileDesc, OutputStream outputStream) throws IOException;

    void deleteFile(T fileDesc) throws IOException;

    boolean isOpen();

    Object getRawClient();

    default void checkState() {
        if (!this.isOpen()) {
            throw new FSClientException(this.getClass().getSimpleName()+"已关闭");
        }
    }

}
