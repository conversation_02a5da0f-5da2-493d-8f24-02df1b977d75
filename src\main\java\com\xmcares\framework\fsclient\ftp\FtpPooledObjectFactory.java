/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/14
 */
package com.xmcares.framework.fsclient.ftp;

import com.xmcares.framework.fsclient.FSClientException;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPClientConfig;
import org.apache.commons.net.ftp.FTPReply;
import org.apache.commons.pool2.PooledObject;
import org.apache.commons.pool2.PooledObjectFactory;
import org.apache.commons.pool2.impl.DefaultPooledObject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

/**
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FtpPooledObjectFactory implements PooledObjectFactory<FTPClient> {
    protected static final Logger logger = LoggerFactory.getLogger(FtpPooledObjectFactory.class);

    private FTPClientConfig config;

    private FtpProperties properties = new FtpProperties();


    @Override
    public PooledObject<FTPClient> makeObject() throws Exception {
        FTPClient client = new FTPClient();
        client.configure(this.config);
        Assert.hasText(this.properties.getUsername(), "username is required");
        if (this.properties.getConnectTimeout() != null) {
            client.setConnectTimeout(this.properties.getConnectTimeout());
        }
        if (this.properties.getDefaultTimeout() != null) {
            client.setDefaultTimeout(this.properties.getDefaultTimeout());
        }
        if (this.properties.getDataTimeout() != null) {
            client.setDataTimeout(this.properties.getDataTimeout());
        }
        client.setControlEncoding(this.properties.getControlEncoding());

        // Connect
        client.connect(this.properties.getHost(), this.properties.getPort());

        if (!FTPReply.isPositiveCompletion(client.getReplyCode())) {
            throw new FSClientException("连接FTP服务器[" +
                    this.properties.getHost() + ":" + this.properties.getPort() + "] 失败.请求检查网络.");
        }
        logger.debug("连接到FTP服务器[{}:{}]", this.properties.getHost(), this.properties.getPort());

        // Login
        if (!client.login(this.properties.getUsername(), this.properties.getPassword())) {
            throw new FSClientException("登录FTP服务器失败: " + client.getReplyString());
        }

        this.updateClientMode(client);
        client.setFileType(this.properties.getFileType());
        client.setBufferSize(this.properties.getBufferSize());

        return new DefaultPooledObject<>(client);
    }

    @Override
    public void destroyObject(PooledObject<FTPClient> pooledObject) throws Exception {
        FTPClient client = pooledObject.getObject();
        if (client != null && client.isConnected()) {
            client.disconnect();
        }
    }

    @Override
    public boolean validateObject(PooledObject<FTPClient> pooledObject) {
        FTPClient client = pooledObject.getObject();
        if (client == null || !client.isConnected()) {
            return false;
        }
        try {
            if (client.sendNoOp()) {
                return true;
            }
        } catch (Exception e) {
            logger.error("验证FTP连接失败: {}", e);
        }
        return false;
    }

    @Override
    public void activateObject(PooledObject<FTPClient> pooledObject) throws Exception {
        //do nothing
    }

    @Override
    public void passivateObject(PooledObject<FTPClient> pooledObject) throws Exception {
        //do nothing
    }


    public void setConfig(FTPClientConfig config) {
        this.config = config;
    }

    public void setProperties(FtpProperties properties) {
        Assert.notNull(properties, "FtpProperties参数不可为NULL");
        this.properties = properties;
    }


    /**
     * Sets the mode of the connection. Only local modes are supported.
     */
    private void updateClientMode(FTPClient client) {
        switch (this.properties.getClientMode()) {
            case FTPClient.ACTIVE_LOCAL_DATA_CONNECTION_MODE:
                client.enterLocalActiveMode();
                break;
            case FTPClient.PASSIVE_LOCAL_DATA_CONNECTION_MODE:
                client.enterLocalPassiveMode();
                break;
            default:
                break;
        }
    }
}
