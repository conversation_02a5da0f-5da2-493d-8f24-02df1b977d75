# 文件服务器完整部署示例
version: '3.8'

services:
  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: minio-server
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - fileserver-network

  # FTP服务器
  ftp-server:
    image: stilliard/pure-ftpd:hardened
    container_name: ftp-server
    ports:
      - "21:21"
      - "30000-30009:30000-30009"
    environment:
      PUBLICHOST: localhost
      FTP_USER_NAME: ftpuser
      FTP_USER_PASS: ftppass123
      FTP_USER_HOME: /home/<USER>
    volumes:
      - ftp_data:/home/<USER>
      - ./ftp-config:/etc/pure-ftpd/conf
    networks:
      - fileserver-network

  # SFTP服务器
  sftp-server:
    image: atmoz/sftp:latest
    container_name: sftp-server
    ports:
      - "2222:22"
    command: sftpuser:sftppass123:1001::upload
    volumes:
      - sftp_data:/home/<USER>/upload
    networks:
      - fileserver-network

  # 应用服务器
  file-service-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: file-service-app
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      XCNF_DATA_FSCLIENT_TYPE: minio
      XCNF_DATA_FSCLIENT_MINIO_ENDPOINT: http://minio:9000
      XCNF_DATA_FSCLIENT_MINIO_ACCESS_KEY: minioadmin
      XCNF_DATA_FSCLIENT_MINIO_SECRET_KEY: minioadmin123
      XCNF_DATA_FSCLIENT_MINIO_DEFAULT_BUCKET: app-files
    depends_on:
      - minio
    networks:
      - fileserver-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/actuator/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - file-service-app
      - minio
    networks:
      - fileserver-network

volumes:
  minio_data:
  ftp_data:
  sftp_data:

networks:
  fileserver-network:
    driver: bridge
