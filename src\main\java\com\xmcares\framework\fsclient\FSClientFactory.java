/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/14
 */
package com.xmcares.framework.fsclient;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public interface FSClientFactory<T extends FSClient> {


    /**
     * 创建fs client
     * @return
     * @throws Exception
     */
    T createFSClient();

    /**
     * 销毁对象
     */
    default  void destroy() {
        //default do nothing
    }
}
