# FTP文件上传错误分析与解决方案

## 🔍 错误分析

### 错误信息
```
com.xmcares.framework.fsclient.FSClientException: Upload文件[xotp-examples.jar]失败：[550 Filename invalid]
```

### 错误原因
**550 Filename invalid** 是FTP服务器返回的错误，表示文件名或路径格式不符合服务器要求。

## 🎯 问题定位

通过分析源码发现以下问题：

### 1. 路径格式问题
您的代码中使用：
```java
.storeName(uploadFilePath + finalFileName)
```

**问题**：
- 如果`uploadFilePath`不是以`/`结尾，会导致路径拼接错误
- Windows风格的路径分隔符`\`在FTP中无效
- 路径可能包含非法字符

### 2. FTP客户端实现细节
从源码分析发现：
- FTP客户端会自动创建目录结构（第84-89行）
- `resolveFileDirs()`方法基于`fileName`解析目录，而不是`storeName`
- FTP不支持attributes（第80-82行会警告并忽略）

### 3. 文件名解析逻辑
```java
// FileDesc接口中的resolveFileDirs方法
default String[] resolveFileDirs() {
    String[] dirs = StringUtils.split(this.getFileName(), "/");
    // 基于fileName而不是storeName解析目录
}
```

## ✅ 解决方案

### 方案1：修正路径格式（推荐）
```java
public FileDescResult uploadFile(MultipartFile file, String fileName, String uploadFilePath) throws IOException {
    String finalFileName = fileName.endsWith(JAR_SUFFIX) ? fileName : fileName + JAR_SUFFIX;
    
    try {
        // 1. 规范化路径格式
        String normalizedPath = normalizeFtpPath(uploadFilePath);
        String fullStorePath = normalizedPath + finalFileName;
        
        // 2. 构建FTP文件描述
        FileDesc fileDesc = FtpFileDesc.builder()
                .fileName(finalFileName)  // 只设置文件名
                .storeName(fullStorePath) // 完整路径
                .build();
        
        // 3. 不要设置attributes（FTP不支持）
        
        FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());
        return result;
        
    } catch (Exception e) {
        logger.error("FTP文件上传异常", e);
        return FileDescResult.builder()
                .success(false)
                .errorMessage("FTP上传失败: " + e.getMessage())
                .build();
    }
}

private String normalizeFtpPath(String path) {
    if (path == null || path.trim().isEmpty()) {
        return "/";
    }
    
    // 统一使用Unix格式的路径分隔符
    String normalized = path.replace("\\", "/");
    
    // 确保以/开头
    if (!normalized.startsWith("/")) {
        normalized = "/" + normalized;
    }
    
    // 确保目录路径以/结尾
    if (!normalized.endsWith("/") && !normalized.contains(".")) {
        normalized = normalized + "/";
    }
    
    // 移除重复的/
    normalized = normalized.replaceAll("/+", "/");
    
    return normalized;
}
```

### 方案2：使用相对路径
```java
// 如果FTP服务器配置了特定的根目录，可以使用相对路径
FileDesc fileDesc = FtpFileDesc.builder()
        .fileName(finalFileName)
        .storeName("plugins/" + finalFileName)  // 相对路径
        .build();
```

### 方案3：预先创建目录
```java
// 在上传前确保目录存在
public boolean ensureDirectoryExists(String directoryPath) {
    try {
        Object rawClient = fsTemplate.getRawClient();
        if (rawClient instanceof FTPClient) {
            FTPClient ftpClient = (FTPClient) rawClient;
            return ftpClient.makeDirectory(directoryPath);
        }
    } catch (Exception e) {
        logger.error("创建目录失败: {}", directoryPath, e);
    }
    return false;
}
```

## 🔧 调试建议

### 1. 添加详细日志
```java
logger.info("上传参数 - fileName: {}, uploadFilePath: {}", fileName, uploadFilePath);
logger.info("处理后 - finalFileName: {}, fullStorePath: {}", finalFileName, fullStorePath);
```

### 2. 验证FTP服务器配置
```bash
# 使用FTP客户端测试
ftp your-ftp-server
# 尝试手动创建目录和上传文件
mkdir /your/upload/path
put local-file.jar remote-file.jar
```

### 3. 检查FTP服务器日志
查看FTP服务器的详细错误日志，了解具体的拒绝原因。

## 📋 最佳实践

### 1. 路径规范
- 始终使用Unix风格的路径分隔符`/`
- 确保路径以`/`开头
- 目录路径以`/`结尾

### 2. 文件名规范
- 避免特殊字符：`\ / : * ? " < > |`
- 控制文件名长度（建议不超过255字符）
- 使用ASCII字符集

### 3. 错误处理
- 捕获并记录详细的错误信息
- 提供有意义的错误消息给用户
- 实现重试机制（对于网络相关错误）

### 4. 配置检查
```yaml
# 确保FTP配置正确
xcnf:
  data:
    fsclient:
      type: ftp
      ftp:
        host: your-ftp-server
        port: 21
        username: your-username
        password: your-password
        client-mode: 2  # PASSIVE模式
        control-encoding: UTF-8
```

## 🚀 快速修复

将您的代码中的这一行：
```java
.storeName(uploadFilePath + finalFileName)
```

替换为：
```java
.storeName(normalizeFtpPath(uploadFilePath) + finalFileName)
```

并添加`normalizeFtpPath`方法，这应该能解决大部分路径相关的问题。
