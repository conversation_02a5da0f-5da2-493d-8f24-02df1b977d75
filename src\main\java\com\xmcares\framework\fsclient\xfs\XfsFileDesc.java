package com.xmcares.framework.fsclient.xfs;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.framework.fsclient.FileDesc;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class XfsFileDesc implements FileDesc {

	private String fileName = "";
	private String storeName = "";
	private Map<String, String> attributes = new HashMap<>();

	private XfsFileDesc() {}

	private XfsFileDesc(String fileName, String storeName, Map<String, String> attributes) {
		this.fileName = fileName;
		this.storeName = storeName;
		this.attributes = attributes != null ? new HashMap<>(attributes) : new HashMap<>();
	}

	@Override public String getFileName() { return fileName == null ? storeName : fileName; }
	@Override public void setFileName(String fileName) { this.fileName = fileName; }
	@Override public String getStoreName() { return storeName == null || storeName.isEmpty() ? fileName : storeName; }
	@Override public void setStoreName(String storeName) { this.storeName = storeName; }
	@Override public Map<String, String> getAttributes() { return attributes; }
	@Override public String getAttribute(String attrName) { return attributes.get(attrName); }
	@Override public void putAttributes(Map<String, String> attributes) { if (attributes != null) this.attributes.putAll(attributes); }
	@Override public void putAttribute(String name, String value) { attributes.put(name, value); }

	@Override
	public String toString() {
		String attris;
		try {
			attris = JacksonJsonUtils.writeObjectAsString(this.attributes);
		} catch (IOException e) {
			attris = "[serialize error]";
		}
		return "XfsFileDesc{" +
				"fileName='" + fileName + '\'' +
				", storeName='" + storeName + '\'' +
				", attributes=" + attris +
				'}';
	}

	public static XfsFileDescBuilder builder() {
		return new XfsFileDescBuilder();
	}

	/**
	 * FTP Builder
	 */
	public static class XfsFileDescBuilder implements Builder<XfsFileDesc> {
		private String fileName = "";
		private String storeName = "";
		private final Map<String, String> attributes = new HashMap<>();

		@Override
		public XfsFileDescBuilder fileName(String fileName) {
			this.fileName = fileName;
			return this;
		}

		@Override
		public XfsFileDescBuilder storeName(String storeName) {
			this.storeName = storeName;
			return this;
		}

		@Override
		public XfsFileDescBuilder attribute(String name, String value) {
			attributes.put(name, value);
			return this;
		}

		@Override
		public XfsFileDescBuilder attributes(Map<String, String> attributes) {
			if (attributes != null) {
				this.attributes.putAll(attributes);
			}
			return this;
		}

		@Override
		public XfsFileDesc build() {
			return new XfsFileDesc(fileName, storeName, attributes);
		}
	}
}
