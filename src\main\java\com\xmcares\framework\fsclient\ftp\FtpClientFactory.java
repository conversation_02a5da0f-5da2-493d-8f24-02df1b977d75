/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/14
 */
package com.xmcares.framework.fsclient.ftp;

import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FSClientFactory;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.ObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class FtpClientFactory implements FSClientFactory<FtpClient> {
    protected static final Logger logger = LoggerFactory.getLogger(FtpClientFactory.class);
    private ObjectPool<FTPClient> clientPool;

    @Override
    public FtpClient createFSClient() {
        FTPClient client = null;
        try {
            client = this.clientPool.borrowObject();
            logger.info("ftp客户端连接池活动数：Actives:{}, Idles:{}", this.clientPool.getNumActive(), this.clientPool.getNumIdle());
            client.changeWorkingDirectory("/");
        } catch (Exception e) {
            throw new FSClientException("获取FTP客户端失败", e);
        }
        return new FtpClient(this.clientPool, client);
    }

    public void setClientPool(ObjectPool<FTPClient> clientPool) {
        Assert.notNull(clientPool, "ftp client pool can't be null.");
        this.clientPool = clientPool;
    }
}
