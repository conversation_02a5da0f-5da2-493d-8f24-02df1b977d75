package com.xmcares.framework.fsclient;

import com.xmcares.framework.fsclient.fastdfs.FdfsFileDesc;
import com.xmcares.framework.fsclient.ftp.FtpFileDesc;
import com.xmcares.framework.fsclient.minio.MinioFileDesc;
import com.xmcares.framework.fsclient.xfs.XfsFileDesc;
import org.junit.Test;

import java.io.IOException;

public class FileDescTest {

	@Test
	public void testFileDesc() throws IOException {
		// FTP
		FileDesc fsDesc = FtpFileDesc.builder()
				.fileName("file.txt")
				.storeName("store/file.txt")
				.attribute("custom", "value")
				.build();
		System.out.println(fsDesc);

		// Minio
		FileDesc minioDesc = MinioFileDesc.builder()
				// 本地文件 路径  可选  上传可不填
				.fileName("path/to/file.txt")
				// 目标存储路径   必填
				.storeName("store/file.txt")
				// 指定存储到 哪个桶，未选择使用默认
				.bucket("my-bucket")
				// 文件类型
				.contentType("application/pdf")
				// 文件 tag 属性
				.attribute(FileDesc.ATTR_TAG_PREFIX + "priv", "yzgt")
				// 文件元数据属性
				.attribute(FileDesc.ATTR_METADATA_PREFIX + "date", "2025-03-19")
				// 区域
				.region("us-east-1")
				.build();
		System.out.println(minioDesc);

		// XFS
		FileDesc ftpDesc = XfsFileDesc.builder()
				.fileName("ftp/file.txt")
				.storeName("remote/file.txt")
				.build();
		System.out.println(ftpDesc);

		// XFS
		FileDesc fdfsDesc = FdfsFileDesc.builder()
				.fileName("ftp/file.txt")
				.storeName("remote/file.txt")
				.build();
		System.out.println(fdfsDesc);
	}
}