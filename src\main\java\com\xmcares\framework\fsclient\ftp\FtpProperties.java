/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/13
 */
package com.xmcares.framework.fsclient.ftp;

import org.apache.commons.net.ftp.FTP;
import org.apache.commons.net.ftp.FTPClient;
import org.springframework.util.Assert;

/**
 * FTP服务器配置
 * <AUTHOR>
 * @since 1.0.0
 */
public class FtpProperties {

    /**
     * FTP host address.
     */
    private String host;
    /**
     * FTP端口(控制端口).
     */
    private int port = FTP.DEFAULT_PORT;
    /**
     * FTP用户名
     */
    private String username;

    /**
     * FTP用户密码
     */
    private String password;

    /**
     * Buffer bytes for buffered data streams.
     */
    private int bufferSize = 2048;

    /**
     * FTP 传输模式：0，ACTIVE模式；2，PASSIVE模式.
     */
    private int clientMode = FTPClient.PASSIVE_LOCAL_DATA_CONNECTION_MODE;

    /**
     * 文件类型.
     */
    private int fileType = FTP.BINARY_FILE_TYPE;

    /**
     * 控制（命令）的数据编码，默认ISO-8859-1.
     */
    private String controlEncoding = FTP.DEFAULT_CONTROL_ENCODING;

    /**
     * 命令连接超时毫秒数（soTimeout），默认0，表示不超时.
     */
    private Integer connectTimeout;

    /**
     *  建立Socket连接超时毫秒数.
     */
    private Integer defaultTimeout;

    /**
     * 数据连接超时毫秒数（soTimeout），默认0，表示不超时.
     */
    private Integer dataTimeout;

    public String getHost() {
        return host;
    }

    public int getPort() {
        return port;
    }

    public String getUsername() {
        return username;
    }

    public String getPassword() {
        return password;
    }

    public int getBufferSize() {
        return bufferSize;
    }


    public int getClientMode() {
        return clientMode;
    }

    public int getFileType() {
        return fileType;
    }


    public String getControlEncoding() {
        return controlEncoding;
    }


    public Integer getConnectTimeout() {
        return connectTimeout;
    }


    public Integer getDefaultTimeout() {
        return defaultTimeout;
    }

    public Integer getDataTimeout() {
        return dataTimeout;
    }


    public void setFileType(int fileType) {
        this.fileType = fileType;
    }

    public void setControlEncoding(String controlEncoding) {
        Assert.hasText(controlEncoding, "'controlEncoding' must not be empty");
        this.controlEncoding = controlEncoding;
    }


    public void setBufferSize(int bufferSize) {
        this.bufferSize = bufferSize;
    }

    public void setHost(String host) {
        Assert.hasText(host, "'host' must not be empty");
        this.host = host;
    }

    public void setPort(int port) {
        Assert.isTrue(port > 0, "Port number should be > 0");
        this.port = port;
    }

    public void setUsername(String user) {
        Assert.hasText(user, "'user' should be a nonempty string");
        this.username = user;
    }

    public void setPassword(String pass) {
        Assert.notNull(pass, "password should not be null");
        this.password = pass;
    }

    /**
     * ACTIVE_LOCAL_DATA_CONNECTION_MODE = 0 <br>
     * A constant indicating the FTP session is expecting all transfers
     * to occur between the client (local) and server and that the server
     * should connect to the client's data port to initiate a data transfer.
     * This is the default data connection mode when and FTPClient instance
     * is created.
     * PASSIVE_LOCAL_DATA_CONNECTION_MODE = 2 <br>
     * A constant indicating the FTP session is expecting all transfers
     * to occur between the client (local) and server and that the server
     * is in passive mode, requiring the client to connect to the
     * server's data port to initiate a transfer.
     *
     * @param clientMode The client mode.
     */
    public void setClientMode(int clientMode) {
        Assert.isTrue(clientMode == FTPClient.ACTIVE_LOCAL_DATA_CONNECTION_MODE ||
                        clientMode == FTPClient.PASSIVE_LOCAL_DATA_CONNECTION_MODE,
                "Only local modes are supported. Was: " + clientMode);
        this.clientMode = clientMode;
    }

    /**
     * Set the connect timeout for the socket.
     * @param connectTimeout the timeout
     */
    public void setConnectTimeout(int connectTimeout) {
        this.connectTimeout = connectTimeout;
    }

    /**
     * Set the (socket option) timeout on the command socket.
     * @param defaultTimeout the timeout.
     */
    public void setDefaultTimeout(int defaultTimeout) {
        this.defaultTimeout = defaultTimeout;
    }

    /**
     * Set the (socket option) timeout on the data connection.
     * @param dataTimeout the timeout.
     */
    public void setDataTimeout(int dataTimeout) {
        this.dataTimeout = dataTimeout;
    }



}
