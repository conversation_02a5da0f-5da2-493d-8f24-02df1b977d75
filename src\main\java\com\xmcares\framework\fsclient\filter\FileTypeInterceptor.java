package com.xmcares.framework.fsclient.filter;

import com.xmcares.framework.commons.util.string.StringUtils;
import com.xmcares.framework.fsclient.FSClientProperties;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 基于 FileDesc 的文件类型拦截
 *
 */
public class FileTypeInterceptor implements FileUploadInterceptor {

	private List<String> allowedTypes = new ArrayList<>();

	private FSClientProperties fsClientProperties;

	public FileTypeInterceptor() {
	}

	@Override
	public FileDescResult preUpload(FileDesc fileDesc, InputStream inputStream) {
		String suffix = getFileExtension(fileDesc);
		if (StringUtils.hasLength(suffix)
				&& !allowedTypes.isEmpty()
				&& !allowedTypes.contains(suffix))
		{
			return FileDescResult.builder()
					.success(false)
					.fileDesc(fileDesc)
					.errorCode("INVALID_TYPE")
					.errorMessage("Unsupported content type: " + suffix)
					.build();
		} else {
			return FileDescResult.builder()
					.success(true)
					.fileDesc(fileDesc)
					.build();
		}
	}

	/**
	 * 获取文件后缀名（扩展名）
	 * @param fileDesc 文件描述对象
	 * @return 文件后缀名（带点，例如 ".txt"），若无后缀或文件名为空则返回 null
	 */
	private String getFileExtension(FileDesc fileDesc) {
		String fileName = fileDesc.getFileName();
		if (StringUtils.hasLength(fileName)) {
			int lastDotIndex = fileName.lastIndexOf('.');
			if (lastDotIndex > 0 && lastDotIndex < fileName.length() - 1) { // 确保点不是第一个或最后一个字符
				return fileName.substring(lastDotIndex + 1); // 返回包含点的后缀，如 ".txt"
			}
		}
		return null; // 无后缀或文件名为空
	}

	public List<String> getDistinctAllowedTypes() {
		if (fsClientProperties != null) {
			List<String> fileType = fsClientProperties.getFilter().getFileType();
			return fileType.stream().distinct().collect(Collectors.toList());
		}
		return new ArrayList<>();
	}

	@Override
	public void setFsClientProperties(FSClientProperties fsClientProperties) {
		this.fsClientProperties = fsClientProperties;
		this.allowedTypes = getDistinctAllowedTypes();
	}
}
