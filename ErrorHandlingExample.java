package com.example.fileserver.exception;

import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FileDescResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.RestControllerAdvice;
import org.springframework.web.multipart.MaxUploadSizeExceededException;

import java.io.IOException;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件服务异常处理器
 */
@RestControllerAdvice
public class FileServerExceptionHandler {
    
    private static final Logger logger = LoggerFactory.getLogger(FileServerExceptionHandler.class);
    
    /**
     * 处理文件服务客户端异常
     */
    @ExceptionHandler(FSClientException.class)
    public ResponseEntity<Map<String, Object>> handleFSClientException(FSClientException e) {
        logger.error("文件服务客户端异常", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "FS_CLIENT_ERROR");
        response.put("message", "文件服务操作失败: " + e.getMessage());
        
        // 根据异常类型返回不同的HTTP状态码
        HttpStatus status = HttpStatus.INTERNAL_SERVER_ERROR;
        if (e.getCause() instanceof ConnectException) {
            status = HttpStatus.SERVICE_UNAVAILABLE;
            response.put("message", "文件服务器连接失败");
        } else if (e.getCause() instanceof SocketTimeoutException) {
            status = HttpStatus.REQUEST_TIMEOUT;
            response.put("message", "文件服务器响应超时");
        }
        
        return ResponseEntity.status(status).body(response);
    }
    
    /**
     * 处理文件上传大小超限异常
     */
    @ExceptionHandler(MaxUploadSizeExceededException.class)
    public ResponseEntity<Map<String, Object>> handleMaxUploadSizeExceeded(MaxUploadSizeExceededException e) {
        logger.warn("文件上传大小超限: {}", e.getMessage());
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "FILE_SIZE_EXCEEDED");
        response.put("message", "文件大小超过限制");
        response.put("maxSize", e.getMaxUploadSize());
        
        return ResponseEntity.status(HttpStatus.PAYLOAD_TOO_LARGE).body(response);
    }
    
    /**
     * 处理IO异常
     */
    @ExceptionHandler(IOException.class)
    public ResponseEntity<Map<String, Object>> handleIOException(IOException e) {
        logger.error("IO异常", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "IO_ERROR");
        response.put("message", "文件操作IO异常: " + e.getMessage());
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
    
    /**
     * 处理通用异常
     */
    @ExceptionHandler(Exception.class)
    public ResponseEntity<Map<String, Object>> handleGenericException(Exception e) {
        logger.error("未处理的异常", e);
        
        Map<String, Object> response = new HashMap<>();
        response.put("success", false);
        response.put("errorCode", "INTERNAL_ERROR");
        response.put("message", "服务器内部错误");
        
        return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).body(response);
    }
}

/**
 * 文件服务操作工具类，包含重试和错误恢复机制
 */
class FileServiceUtils {
    
    private static final Logger logger = LoggerFactory.getLogger(FileServiceUtils.class);
    private static final int MAX_RETRY_ATTEMPTS = 3;
    private static final long RETRY_DELAY_MS = 1000;
    
    /**
     * 带重试机制的文件操作
     */
    public static <T> T executeWithRetry(FileOperation<T> operation, String operationName) throws Exception {
        Exception lastException = null;
        
        for (int attempt = 1; attempt <= MAX_RETRY_ATTEMPTS; attempt++) {
            try {
                logger.debug("执行文件操作: {} (尝试 {}/{})", operationName, attempt, MAX_RETRY_ATTEMPTS);
                return operation.execute();
                
            } catch (Exception e) {
                lastException = e;
                logger.warn("文件操作失败: {} (尝试 {}/{}): {}", 
                           operationName, attempt, MAX_RETRY_ATTEMPTS, e.getMessage());
                
                // 如果不是最后一次尝试，等待后重试
                if (attempt < MAX_RETRY_ATTEMPTS) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("操作被中断", ie);
                    }
                }
            }
        }
        
        // 所有重试都失败，抛出最后一个异常
        throw new RuntimeException("文件操作失败，已重试 " + MAX_RETRY_ATTEMPTS + " 次: " + operationName, lastException);
    }
    
    /**
     * 检查文件操作结果并处理错误
     */
    public static void validateResult(FileDescResult result, String operation) throws FSClientException {
        if (result == null) {
            throw new FSClientException(operation + " 返回结果为空");
        }
        
        if (!result.isSuccess()) {
            String errorMessage = result.getErrorMessage() != null ? 
                                 result.getErrorMessage() : "未知错误";
            String errorCode = result.getErrorCode() != null ? 
                              result.getErrorCode() : "UNKNOWN_ERROR";
            
            throw new FSClientException(String.format("%s 失败 [%s]: %s", 
                                                     operation, errorCode, errorMessage));
        }
    }
    
    /**
     * 文件操作接口
     */
    @FunctionalInterface
    public interface FileOperation<T> {
        T execute() throws Exception;
    }
}
