/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/10/28
 */
package com.xmcares.framework.fsclient.xfs;

import org.apache.cxf.transports.http.configuration.HTTPClientPolicy;

/**
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class XfsProperties {

    /**
     * 厦门凯亚文件服务wsdl url
     */
    private String wsdlUrl;

    /**
     * 厦门凯亚文件服务客户端配置
     */
    private HTTPClientPolicy httpPolicy;

    public String getWsdlUrl() {
        return wsdlUrl;
    }

    public void setWsdlUrl(String wsdlUrl) {
        this.wsdlUrl = wsdlUrl;
    }

    public HTTPClientPolicy getHttpPolicy() {
        return httpPolicy;
    }

    public void setHttpPolicy(HTTPClientPolicy httpPolicy) {
        this.httpPolicy = httpPolicy;
    }
}
