package com.xmcares.framework.fsclient.local;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.framework.fsclient.FileDesc;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class LocalFileDesc implements FileDesc {

	private String fileName = "";
	private String storeName = "";
	private Map<String, String> attributes = new HashMap<>();

	private LocalFileDesc() {}

	private LocalFileDesc(String fileName, String storeName, Map<String, String> attributes) {
		this.fileName = fileName;
		this.storeName = storeName;
		this.attributes = attributes != null ? new HashMap<>(attributes) : new HashMap<>();
	}

	@Override public String getFileName() { return fileName == null ? storeName : fileName; }
	@Override public void setFileName(String fileName) { this.fileName = fileName; }
	@Override public String getStoreName() { return storeName == null || storeName.isEmpty() ? fileName : storeName; }
	@Override public void setStoreName(String storeName) { this.storeName = storeName; }
	@Override public Map<String, String> getAttributes() { return attributes; }
	@Override public String getAttribute(String attrName) { return attributes.get(attrName); }
	@Override public void putAttributes(Map<String, String> attributes) { if (attributes != null) this.attributes.putAll(attributes); }
	@Override public void putAttribute(String name, String value) { attributes.put(name, value); }

	@Override
	public String toString() {
		String attris;
		try {
			attris = JacksonJsonUtils.writeObjectAsString(this.attributes);
		} catch (IOException e) {
			attris = "[serialize error]";
		}
		return "LocalFileDesc{" +
				"fileName='" + fileName + '\'' +
				", storeName='" + storeName + '\'' +
				", attributes=" + attris +
				'}';
	}

	static LocalFileDesc.LocalFileDescBuilder builder() {
		return new LocalFileDesc.LocalFileDescBuilder();
	}

	/**
	 * FTP Builder
	 */
	public static class LocalFileDescBuilder implements FileDesc.Builder<LocalFileDesc> {
		private String fileName = "";
		private String storeName = "";
		private final Map<String, String> attributes = new HashMap<>();

		@Override
		public LocalFileDescBuilder fileName(String fileName) {
			this.fileName = fileName;
			return this;
		}

		@Override
		public LocalFileDescBuilder storeName(String storeName) {
			this.storeName = storeName;
			return this;
		}

		@Override
		public LocalFileDescBuilder attribute(String name, String value) {
			attributes.put(name, value);
			return this;
		}

		@Override
		public LocalFileDescBuilder attributes(Map<String, String> attributes) {
			if (attributes != null) {
				this.attributes.putAll(attributes);
			}
			return this;
		}

		@Override
		public LocalFileDesc build() {
			return new LocalFileDesc(fileName, storeName, attributes);
		}
	}
}