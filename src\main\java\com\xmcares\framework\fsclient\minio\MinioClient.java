/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/5
 */
package com.xmcares.framework.fsclient.minio;

import com.google.common.io.ByteStreams;
import com.xmcares.framework.commons.util.string.StringUtils;
import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import io.minio.*;
import io.minio.errors.MinioException;
import okhttp3.Headers;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.GeneralSecurityException;
import java.util.HashMap;
import java.util.Map;

import static com.xmcares.framework.fsclient.FileDesc.*;

/**
 * MinIO 客户端
 *
 * <AUTHOR>
 * @since 1.3.0
 */
public class MinioClient implements FSClient {

	private static final String USER_META_PREFIX = "x-amz-meta-";

	protected Logger logger = LoggerFactory.getLogger(this.getClass());

	private io.minio.MinioClient rawClient;

	private String defaultBucket;

	public MinioClient(io.minio.MinioClient rawClient, String defaultBucket) {
		this.rawClient = rawClient;
		this.defaultBucket = defaultBucket;
	}

	@Override
	public FileDescResult uploadFile(FileDesc fileDesc, InputStream inputStream) throws IOException {
		FileDescResult result = null;
		checkState();
		String bucketName = this.getBucketName(fileDesc);
		try {
			BucketExistsArgs existArgs = BucketExistsArgs.builder().bucket(bucketName).build();
			if (!this.rawClient.bucketExists(existArgs)) {
				this.rawClient.makeBucket(MakeBucketArgs.builder().bucket(bucketName).build());
			}
		} catch (MinioException | GeneralSecurityException e) {
			throw new FSClientException(String.format("MinIO bucket[%s] Existing异常", bucketName), e);
		}
		ObjectWriteResponse resp = null;
		try {
			PutObjectArgs.Builder builder = PutObjectArgs.builder()
					.stream(inputStream, inputStream.available(), 8L * 1024 * 1024)
					.bucket(bucketName)
					.object(fileDesc.getStoreName());

			if (StringUtils.hasLength(fileDesc.getAttribute(ATTR_REGION))) {
				builder.region(fileDesc.getAttribute(ATTR_REGION));
			}

			if (StringUtils.hasLength(fileDesc.getAttribute(ATTR_CONTENT_TYPE))) {
				builder.contentType(fileDesc.getAttribute(ATTR_CONTENT_TYPE));
			}
			// 添加 Tag 的判定和设置
			Map<String, String> tags = extractTags(fileDesc);
			if (!tags.isEmpty()) {
				builder.tags(tags);
			}
			// 添加 Metadata 的判定和设置
			Map<String, String> metadata = extractMetadata(fileDesc);
			if (!metadata.isEmpty()) {
				builder.userMetadata(metadata);
			}

			resp = this.rawClient.putObject(builder.build());
			logger.debug("MinIO文件上传成功[bucket:{}, region:{}, path:{}, version:{}, etag:{}]",
					resp.bucket(), resp.region(), fileDesc.getStoreName(), resp.versionId(), resp.etag());
			return FileDescResult.builder()
					.fileDesc(fileDesc)
					.success(true)
					.build();
		} catch (MinioException | GeneralSecurityException e) {
			throw new FSClientException(
					String.format("MinIO upload bucket:path[%s: %s] 异常", bucketName, fileDesc.getStoreName()), e);
		} finally {

		}
	}

	@Override
	public FileDescResult downloadFile(FileDesc fileDesc, OutputStream outputStream) throws IOException {
		checkState();
		String bucketName = this.getBucketName(fileDesc);
		GetObjectResponse resp = null;
		try {
			resp = this.rawClient.getObject(GetObjectArgs.builder()
					.bucket(bucketName)
					.object(fileDesc.getStoreName())
					.build());

			ByteStreams.copy(resp, outputStream);

			Headers headers = resp.headers();
			FileDescResult.Builder builder = FileDescResult.builder();
			Map<String, String> userAttributes = getUserAttributes(headers);
			for (Map.Entry<String, String> entry : userAttributes.entrySet()) {
				builder.metadata(entry.getKey(), entry.getValue());
			}
			return builder.fileDesc(fileDesc)
					.success(true)
					.build();
		} catch (MinioException | GeneralSecurityException e) {
			throw new FSClientException(
					String.format("MinIO download bucket:path[%s: %s] 异常", bucketName, fileDesc.getStoreName()), e);
		} finally {
			if (resp != null) {
				resp.close();
			}
		}
	}

	@Override
	public void deleteFile(FileDesc fileDesc) throws IOException {
		checkState();
		String bucketName = this.getBucketName(fileDesc);
		try {
			this.rawClient.removeObject(RemoveObjectArgs.builder()
					.bucket(bucketName)
					.object(fileDesc.getStoreName())
					.build());
		} catch (MinioException | GeneralSecurityException e) {
			throw new FSClientException(
					String.format("MinIO delete bucket:path[%s: %s] 异常", bucketName, fileDesc.getStoreName()), e);
		}
	}

	@Override
	public boolean isOpen() {
		return this.rawClient != null;
	}

	@Override
	public void close() throws IOException {
		//do nothing
	}

	public io.minio.MinioClient getRawClient() {
		return this.rawClient;
	}

	public String getDefaultBucket() {
		return defaultBucket;
	}

	protected String getBucketName(FileDesc fileDesc) {
		String bucket = null;
		if (fileDesc == null) {
			return this.defaultBucket;
		}
		if (fileDesc.getClass().isAssignableFrom(MinioFileDesc.class)) {
			MinioFileDesc minioFileDesc = (MinioFileDesc) fileDesc;
			bucket = minioFileDesc.getBucket();
		}
		return StringUtils.hasLength(bucket) ? bucket : this.defaultBucket;
	}

	private Map<String, String> getUserAttributes(Headers headers) {
		Map<String, String> result = new HashMap<>(2);
		int i = 0;
		String name;
		for (int size = headers.size(); i < size; ++i) {
			name = headers.name(i);
			if (StringUtils.startsWithIgnoreCase(name, USER_META_PREFIX)) {
				result.put(name.substring(USER_META_PREFIX.length()), headers.value(i));
			}
		}
		return result;
	}

	/**
	 * 从 FileDesc 中提取 Tag 信息
	 * @param fileDesc 文件描述对象
	 * @return Tag 的 Map，键值对形式
	 */
	private Map<String, String> extractTags(FileDesc fileDesc) {
		return extractByPrefix(fileDesc, ATTR_TAG_PREFIX);

	}

	/**
	 * 从 FileDesc 中提取 Tag 信息
	 * @param fileDesc 文件描述对象
	 * @return Tag 的 Map，键值对形式
	 */
	private Map<String, String> extractMetadata(FileDesc fileDesc) {
		return extractByPrefix(fileDesc, ATTR_METADATA_PREFIX);
	}

	private Map<String, String> extractByPrefix(FileDesc fileDesc, String prefix) {
		Map<String, String> result = new HashMap<>();
		Map<String, String> attributes = fileDesc.getAttributes();

		for (Map.Entry<String, String> entry : attributes.entrySet()) {
			if (entry.getKey().startsWith(prefix)) {
				String key = entry.getKey().substring(prefix.length()); // 去掉前缀
				result.put(key, entry.getValue());
			}
		}
		return result;
	}

}
