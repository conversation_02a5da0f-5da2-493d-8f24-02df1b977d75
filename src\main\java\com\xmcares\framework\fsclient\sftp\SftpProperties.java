/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2021/5/19
 */
package com.xmcares.framework.fsclient.sftp;

import com.jcraft.jsch.UserInfo;
import org.springframework.core.io.Resource;

import java.time.Duration;
import java.util.Properties;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
public class SftpProperties {

    /**
     * SFTP host
     */
    private String host = "localhost";

    /**
     * SFTP PORT, default 22.
     */
    private int port = 22;

    /**
     * SFTP username.
     */
    private String username = "test";

    /**
     * SFTP password.
     */
    private String password = "test";

    /**
     * 从配置文件读取KnowHosts
     */
    private Resource knownHosts;

    /**
     * 从配置文件读取privateKey
     */
    private Resource privateKey;

    /**
     * PrivateKey passphrase
     */
    private String privateKeyPassphrase;

    /**
     * Session配置
     */
    private Properties session;

    /**
     * 超时配置
     */
    private Duration sessionTimeout;


    private String clientVersion;

    private String hostKeyAlias;

    private Duration serverAliveInterval;

    private Integer serverAliveCountMax;

    private Boolean enableDaemonThread;

    private UserInfo userInfo;

    private boolean allowUnknownKeys = false;

    private Duration channelConnectTimeout;


    public String getHost() {
        return host;
    }

    public void setHost(String host) {
        this.host = host;
    }

    public int getPort() {
        return port;
    }

    public void setPort(int port) {
        this.port = port;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public Resource getKnownHosts() {
        return knownHosts;
    }

    public void setKnownHosts(Resource knownHosts) {
        this.knownHosts = knownHosts;
    }

    public Resource getPrivateKey() {
        return privateKey;
    }

    public void setPrivateKey(Resource privateKey) {
        this.privateKey = privateKey;
    }

    public String getPrivateKeyPassphrase() {
        return privateKeyPassphrase;
    }

    public void setPrivateKeyPassphrase(String privateKeyPassphrase) {
        this.privateKeyPassphrase = privateKeyPassphrase;
    }

    public Properties getSession() {
        return session;
    }

    public void setSession(Properties session) {
        this.session = session;
    }


    public Duration getSessionTimeout() {
        return sessionTimeout;
    }

    public void setSessionTimeout(Duration sessionTimeout) {
        this.sessionTimeout = sessionTimeout;
    }

    public String getClientVersion() {
        return clientVersion;
    }

    public void setClientVersion(String clientVersion) {
        this.clientVersion = clientVersion;
    }

    public String getHostKeyAlias() {
        return hostKeyAlias;
    }

    public void setHostKeyAlias(String hostKeyAlias) {
        this.hostKeyAlias = hostKeyAlias;
    }

    public Duration getServerAliveInterval() {
        return serverAliveInterval;
    }

    public void setServerAliveInterval(Duration serverAliveInterval) {
        this.serverAliveInterval = serverAliveInterval;
    }

    public Integer getServerAliveCountMax() {
        return serverAliveCountMax;
    }

    public void setServerAliveCountMax(Integer serverAliveCountMax) {
        this.serverAliveCountMax = serverAliveCountMax;
    }

    public Boolean getEnableDaemonThread() {
        return enableDaemonThread;
    }

    public void setEnableDaemonThread(Boolean enableDaemonThread) {
        this.enableDaemonThread = enableDaemonThread;
    }

    public UserInfo getUserInfo() {
        return userInfo;
    }

    public void setUserInfo(UserInfo userInfo) {
        this.userInfo = userInfo;
    }

    public boolean isAllowUnknownKeys() {
        return allowUnknownKeys;
    }

    public void setAllowUnknownKeys(boolean allowUnknownKeys) {
        this.allowUnknownKeys = allowUnknownKeys;
    }

    public Duration getChannelConnectTimeout() {
        return channelConnectTimeout;
    }

    public void setChannelConnectTimeout(Duration channelConnectTimeout) {
        this.channelConnectTimeout = channelConnectTimeout;
    }
}
