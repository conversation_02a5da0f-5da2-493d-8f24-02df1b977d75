/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/5
 */
package com.xmcares.framework.fsclient.minio;

import com.xmcares.framework.fsclient.FSClientFactory;
import okhttp3.OkHttpClient;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @since 1.3.0
 */
public class MinioClientFactory implements FSClientFactory<MinioClient> {

    private final MinioProperties properties;
    private final OkHttpClient httpClient;
    private volatile MinioClient minioClient;

    /**
     * 构造方法，必须的配置属性通过构造函数注入以保证强不变性
     * @param properties 配置属性对象（不可为空）
     * @throws IllegalArgumentException 当必要参数缺失时抛出
     */
    public MinioClientFactory(MinioProperties properties) {
        this(properties, null);
    }

    /**
     * 全量构造方法，支持可选的自定义HTTP客户端
     * @param properties 配置属性对象（不可为空）
     * @param httpClient 自定义的HTTP客户端（可为null）
     */
    public MinioClientFactory(MinioProperties properties, OkHttpClient httpClient) {
        Assert.notNull(properties, "MinioProperties must not be null");
        Assert.hasText(properties.getEndpoint(), "Endpoint must not be empty");
        Assert.hasText(properties.getAccessKey(), "AccessKey must not be empty");
        Assert.hasText(properties.getSecretKey(), "SecretKey must not be empty");
        Assert.hasText(properties.getDefaultBucket(), "DefaultBucket must be configured");

        OkHttpClient okHttpClient = httpClient;
        if(okHttpClient == null) {
            okHttpClient = new OkHttpClient();
        }
        this.properties = properties;
        this.httpClient = okHttpClient;
    }

    @Override
    public MinioClient createFSClient() {
        if (minioClient == null) {
            synchronized (this) {
                if (minioClient == null) {
                    minioClient = getFSClient();
                }
            }
        }
        return minioClient;
    }


    private MinioClient getFSClient() {
        try {
            io.minio.MinioClient.Builder builder = io.minio.MinioClient.builder()
                    .endpoint(properties.getEndpoint())
                    .credentials(properties.getAccessKey(), properties.getSecretKey());

            if (httpClient != null) {
                builder.httpClient(httpClient);
            }

            io.minio.MinioClient rawClient = builder.build();
            return new MinioClient(rawClient, properties.getDefaultBucket());
        } catch (Exception e) {
            throw new IllegalStateException("Failed to create Minio client", e);
        }
    }

    @Override
    public void destroy() {
        minioClient = null;
    }
}
