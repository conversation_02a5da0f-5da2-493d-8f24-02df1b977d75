package com.example.fileserver;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 文件服务器应用启动类
 * 
 * 注意：xcnf-data-fsclient已经通过spring.factories自动配置，
 * 无需额外的@EnableAutoConfiguration或@ComponentScan
 */
@SpringBootApplication
public class FileServerApplication {
    
    public static void main(String[] args) {
        SpringApplication.run(FileServerApplication.class, args);
    }
}
