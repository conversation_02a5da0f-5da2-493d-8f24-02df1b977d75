/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/14
 */
package com.xmcares.framework.fsclient.test;

import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FSClientAutoConfiguration;
import com.xmcares.framework.fsclient.FSClientFactory;
import com.xmcares.framework.fsclient.FileDesc;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.*;
import java.util.Random;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = FSClientAutoConfiguration.class)
@TestPropertySource("classpath:application-test.properties")
public class FSClientTest {

    private static final Logger logger = LoggerFactory.getLogger(FSClientTest.class);

    @Autowired
    FSClientFactory clientFactory;

    @Test
    public void test_download_file() {
        try (FSClient fsclient = clientFactory.createFSClient()) {
            File file = new File("D:\\temp\\1.properties");
            OutputStream outputStream = new BufferedOutputStream(new FileOutputStream(file));
            fsclient.downloadFile(new FileDesc.FileDescImpl("/projectUploadFile/xbdp/PLUGIN_TEMP/writer/2.json",
                    "/projectUploadFile/xbdp/PLUGIN_TEMP/writer/2.json"), outputStream);
            outputStream.close();
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Test
    public void test_delete_file() {
        String tempPath = "/projectUploadFile/xcnfLowcode/originalImage/2024052416412074037557.jpg";
        try (FSClient fsClient = clientFactory.createFSClient()) {
            fsClient.deleteFile(new FileDesc.FileDescImpl(tempPath, tempPath));
        } catch (Exception e) {
            logger.error(e.getMessage());
        }
    }

    @Test
    public void test_upload_file() {
        File fs = new File("/Users/<USER>/Downloads/test/");
        File[] files = fs.listFiles();
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (File file : files) {
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    FSClient fsclient = null;
                    try {
                        FileDesc fileDesc = new FileDesc.FileDescImpl();
                        fileDesc.setFileName("/elm1/lme/" + file.getName());
                        fsclient = clientFactory.createFSClient();
                        FileInputStream fis = new FileInputStream(file);
                        fsclient.uploadFile(fileDesc, new BufferedInputStream(fis));
                        fsclient.close();
                        System.out.println("===========");
                    } catch (Exception e) {
                        logger.error(e.getMessage());
                    } finally {
                        if (fsclient != null) {
                            try {
                                fsclient.close();
                            } catch (IOException e) {
                                logger.error(e.getMessage());
                            }
                        }
                    }
                }
            });

        }
        executorService.shutdown();
        System.out.println("!!!!!!!!!!!!!!!!!");
    }

    @Test
    public void test_ftpclient_pool() throws InterruptedException {
        ExecutorService executorService = Executors.newCachedThreadPool();
        for (int i = 0; i < 100; i++) {
            executorService.submit(new Runnable() {
                @Override
                public void run() {
                    try (FSClient fsClient = clientFactory.createFSClient()) {
                        Thread.sleep(new Random().nextInt(5000));
                    } catch (IOException e) {
                        System.out.println("IO:" + e.getMessage());
                    } catch (Exception e) {
                        System.out.println(" E:" + e.getMessage());
                    }
                }
            });
        }
        executorService.awaitTermination(120, TimeUnit.SECONDS);
    }
}
