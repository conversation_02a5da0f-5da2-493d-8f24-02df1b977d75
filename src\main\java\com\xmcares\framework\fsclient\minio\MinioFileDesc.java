/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/7
 */
package com.xmcares.framework.fsclient.minio;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.framework.fsclient.FileDesc;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * MinIO 文件描述
 * <AUTHOR>
 * @since 1.1.0
 */
public class MinioFileDesc implements FileDesc {

    private String fileName = "";
    private String storeName = "";
    private Map<String, String> attributes = new HashMap<>();

    MinioFileDesc() {}

    MinioFileDesc(String fileName, String storeName, Map<String, String> attributes) {
        this.fileName = fileName;
        this.storeName = storeName;
        this.attributes = attributes != null ? new HashMap<>(attributes) : new HashMap<>();
    }

    @Override public String getFileName() { return fileName == null ? storeName : fileName; }
    @Override public void setFileName(String fileName) { this.fileName = fileName; }
    @Override public String getStoreName() { return storeName == null || storeName.isEmpty() ? fileName : storeName; }
    @Override public void setStoreName(String storeName) { this.storeName = storeName; }
    @Override public Map<String, String> getAttributes() { return attributes; }
    @Override public String getAttribute(String attrName) { return attributes.get(attrName); }
    @Override public void putAttributes(Map<String, String> attributes) { if (attributes != null) this.attributes.putAll(attributes); }
    @Override public void putAttribute(String name, String value) { attributes.put(name, value); }

    // Minio 特定方法
    public String getBucket() { return getAttribute(ATTR_BUCKET); }
    public String getContentType() { return getAttribute(ATTR_CONTENT_TYPE); }
    public String getRegion() { return getAttribute(ATTR_REGION); }

    @Override
    public String toString() {
        String attris;
        try {
            attris = JacksonJsonUtils.writeObjectAsString(this.attributes);
        } catch (IOException e) {
            attris = "[serialize error]";
        }
        return "MinioFileDesc{" +
                "fileName='" + fileName + '\'' +
                ", storeName='" + storeName + '\'' +
                ", attributes=" + attris +
                '}';
    }

    public static MinioFileDescBuilder builder() {
        return new MinioFileDescBuilder();
    }

    /**
     * Minio Builder
     */
    public static class MinioFileDescBuilder implements FileDesc.Builder<MinioFileDesc> {
        private String fileName = "";
        private String storeName = "";
        private final Map<String, String> attributes = new HashMap<>();

        @Override
        public MinioFileDescBuilder fileName(String fileName) {
            this.fileName = fileName;
            return this;
        }

        @Override
        public MinioFileDescBuilder storeName(String storeName) {
            this.storeName = storeName;
            return this;
        }

        @Override
        public MinioFileDescBuilder attribute(String name, String value) {
            attributes.put(name, value);
            return this;
        }

        @Override
        public MinioFileDescBuilder attributes(Map<String, String> attributes) {
            if (attributes != null) {
                this.attributes.putAll(attributes);
            }
            return this;
        }

        // Minio 特定方法
        public MinioFileDescBuilder bucket(String bucket) {
            attributes.put(FileDesc.ATTR_BUCKET, bucket);
            return this;
        }

        public MinioFileDescBuilder contentType(String contentType) {
            attributes.put(FileDesc.ATTR_CONTENT_TYPE, contentType);
            return this;
        }

        public MinioFileDescBuilder region(String region) {
            attributes.put(FileDesc.ATTR_REGION, region);
            return this;
        }

        @Override
        public MinioFileDesc build() {
            return new MinioFileDesc(fileName, storeName, attributes);
        }
    }
}
