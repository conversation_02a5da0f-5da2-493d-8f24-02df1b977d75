# 网络和防火墙配置指南

## 1. 端口配置要求

### MinIO
- **默认端口**: 9000 (API), 9001 (Console)
- **协议**: HTTP/HTTPS
- **防火墙规则**: 
  ```bash
  # 允许MinIO API端口
  sudo ufw allow 9000/tcp
  # 允许MinIO Console端口（可选）
  sudo ufw allow 9001/tcp
  ```

### FTP
- **控制端口**: 21 (默认)
- **数据端口**: 20 (主动模式) 或 动态端口范围 (被动模式)
- **协议**: TCP
- **防火墙规则**:
  ```bash
  # FTP控制端口
  sudo ufw allow 21/tcp
  # 被动模式端口范围（需要在FTP服务器配置）
  sudo ufw allow 50000:51000/tcp
  ```

### SFTP
- **默认端口**: 22
- **协议**: SSH/TCP
- **防火墙规则**:
  ```bash
  sudo ufw allow 22/tcp
  ```

### FastDFS
- **Tracker端口**: 22122 (默认)
- **Storage端口**: 23000 (默认)
- **HTTP端口**: 80 或 8080
- **防火墙规则**:
  ```bash
  sudo ufw allow 22122/tcp  # Tracker
  sudo ufw allow 23000/tcp  # Storage
  sudo ufw allow 80/tcp     # HTTP
  ```

### WebService (XFS)
- **端口**: 根据部署配置 (如 8080, 8085)
- **协议**: HTTP/HTTPS
- **防火墙规则**:
  ```bash
  sudo ufw allow 8085/tcp
  ```

## 2. 网络连接配置

### 客户端网络配置
```yaml
# 网络超时配置示例
xcnf:
  data:
    fsclient:
      # FTP网络配置
      ftp:
        connect-timeout: 30000      # 连接超时(毫秒)
        default-timeout: 60000      # 默认超时
        data-timeout: 60000         # 数据传输超时
      
      # SFTP网络配置
      sftp:
        session-timeout: 30s        # 会话超时
        channel-connect-timeout: 10s # 通道连接超时
        server-alive-interval: 60s   # 心跳间隔
        server-alive-count-max: 3    # 最大心跳失败次数
      
      # WebService网络配置
      xfs:
        http-policy:
          connection-timeout: 30000  # 连接超时
          receive-timeout: 60000     # 接收超时
```

## 3. 代理和负载均衡

### Nginx代理配置示例
```nginx
# MinIO代理配置
upstream minio_backend {
    server *************:9000;
    server *************:9000;
}

server {
    listen 80;
    server_name files.example.com;
    
    location / {
        proxy_pass http://minio_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # 文件上传大小限制
        client_max_body_size 100M;
        
        # 超时配置
        proxy_connect_timeout 30s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }
}
```

## 4. SSL/TLS配置

### MinIO HTTPS配置
```yaml
xcnf:
  data:
    fsclient:
      minio:
        endpoint: https://minio.example.com  # 使用HTTPS
        access-key: your-access-key
        secret-key: your-secret-key
```

### SFTP密钥认证配置
```yaml
xcnf:
  data:
    fsclient:
      sftp:
        host: sftp.example.com
        port: 22
        username: sftpuser
        # 使用私钥认证
        private-key: classpath:ssh/id_rsa
        private-key-passphrase: your-passphrase
        known-hosts: classpath:ssh/known_hosts
        allow-unknown-keys: false  # 严格主机密钥验证
```

## 5. 安全组和防火墙规则

### AWS安全组配置
```json
{
  "SecurityGroupRules": [
    {
      "IpProtocol": "tcp",
      "FromPort": 9000,
      "ToPort": 9000,
      "CidrIp": "10.0.0.0/8",
      "Description": "MinIO API access from internal network"
    },
    {
      "IpProtocol": "tcp", 
      "FromPort": 22,
      "ToPort": 22,
      "CidrIp": "10.0.0.0/8",
      "Description": "SFTP access from internal network"
    }
  ]
}
```

### 阿里云安全组规则
- **协议类型**: TCP
- **端口范围**: 9000/9000 (MinIO)
- **授权对象**: 内网IP段 (如 **********/12)
- **描述**: MinIO文件服务访问

## 6. 网络故障排查

### 连接测试命令
```bash
# 测试MinIO连接
curl -I http://minio-server:9000/minio/health/live

# 测试FTP连接
telnet ftp-server 21

# 测试SFTP连接
ssh -p 22 user@sftp-server

# 测试端口连通性
nc -zv server-ip port
```

### 常见网络问题
1. **连接超时**: 检查防火墙规则和网络路由
2. **FTP被动模式问题**: 配置正确的端口范围
3. **SSL证书问题**: 验证证书有效性和域名匹配
4. **代理配置问题**: 检查代理服务器配置和转发规则
