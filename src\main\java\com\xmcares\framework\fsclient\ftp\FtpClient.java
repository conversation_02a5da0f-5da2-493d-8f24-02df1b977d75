/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/13
 */
package com.xmcares.framework.fsclient.ftp;

import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.pool2.ObjectPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class FtpClient implements FSClient {
    protected static final Logger logger = LoggerFactory.getLogger(FtpClient.class);

    private ObjectPool<FTPClient> clientPool;
    private FTPClient client;


    FtpClient(ObjectPool<FTPClient> clientPool, FTPClient client) {
        this.clientPool = clientPool;
        this.client = client;
    }

    @Override
    public boolean isOpen() {
        if (this.client == null || !this.client.isConnected()) {
            return false;
        }
        try {
            return this.client.sendNoOp();
        } catch (Exception e) {
            logger.error("验证FTP连接失败: {}", e);
        }
        return false;
    }

    @Override
    public Object getRawClient() {
        return this.client;
    }

    @Override
    public void close() throws IOException {
        if (this.clientPool == null || this.client == null) {
            return;
        }
        try {
            this.clientPool.returnObject(this.client);
        } catch (Exception e) {
            logger.error("关闭ftp client错误：", e);
            try {
                this.clientPool.invalidateObject(this.client);
            } catch (Exception ex) {
                //do nothing
            }
        } finally {
            this.client = null;
            this.clientPool = null;
        }
    }

    @Override
    public FileDescResult uploadFile(FileDesc fileDesc, InputStream inputStream) throws IOException {
        checkState();
        if (fileDesc.getAttributes() != null && !fileDesc.getAttributes().isEmpty()) {
            logger.warn("FTP 文件服务不支持添加额外Attributes(已忽略掉)");
        }

        String[] dirs = fileDesc.resolveFileDirs();
        StringBuilder sb = new StringBuilder();
        for (int i = 0, l = dirs.length; i < l; i++) {
            sb.append("/").append(dirs[i]);
            this.client.makeDirectory(sb.toString());
        }
        if (!this.client.storeFile(fileDesc.getStoreName(), inputStream)) {
            throw new FSClientException(String.format("Upload文件[%s]失败：[%s]",
                    fileDesc.getFileName(), this.client.getReplyString()));
        }
        return null;
    }


    @Override
    public FileDescResult downloadFile(FileDesc fileDesc, OutputStream outputStream) throws IOException {
        checkState();
        if (!this.client.retrieveFile(fileDesc.getStoreName(), outputStream)) {
            throw new FSClientException("Retrieve文件[" + fileDesc.getStoreName() + "]失败: " + this.client.getReplyString());
        }
        return null;
    }

    @Override
    public void deleteFile(FileDesc fileDesc) throws IOException {
        checkState();
        if (!this.client.deleteFile(fileDesc.getStoreName())) {
            throw new FSClientException("Delete文件[" + fileDesc.getStoreName() + "]失败: " + this.client.getReplyString());
        }
    }


}
