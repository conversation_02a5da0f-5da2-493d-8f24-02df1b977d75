/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2021/5/18
 */
package com.xmcares.framework.fsclient.sftp;

import com.jcraft.jsch.JSch;
import com.jcraft.jsch.JSchException;
import com.jcraft.jsch.Session;
import com.jcraft.jsch.UserInfo;
import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FSClientFactory;
import org.springframework.util.FileCopyUtils;
import org.springframework.util.StringUtils;

import java.io.IOException;
import java.time.Duration;
import java.util.concurrent.locks.Lock;
import java.util.concurrent.locks.ReentrantLock;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
public class SftpClientFactory implements FSClientFactory<SftpClient> {

    private JSch jsch;

    private Session session;

    private Lock lock = new ReentrantLock();


    private SftpProperties properties = new SftpProperties();


    public SftpClientFactory(SftpProperties properties) {
        this.properties = properties;
        this.jsch = new JSch();
    }

    @Override
    public SftpClient createFSClient() {
        SftpClient client;
        lock.lock();
        try {
            if (session == null || !session.isConnected()) {
                session = initJschSession();
            }
        } catch (JSchException e) {
            throw new FSClientException("初始化SFTP session失败", e);
        } catch (IOException e) {
            throw new FSClientException("初始化SFTP session失败", e);
        } finally {
            lock.unlock();
        }
        client = new SftpClient(session);
        Duration channelTimeout = this.properties.getChannelConnectTimeout();
        if (channelTimeout != null) {
            client.setChannelConnectTimeout((int)channelTimeout.toMillis());
        }
        client.connect();
        return client;
    }

    private Session initJschSession() throws JSchException, IOException {

        if (this.properties.getKnownHosts() != null) {
            this.jsch.setKnownHosts(this.properties.getKnownHosts().getInputStream());
        }

        UserInfo userInfo = this.properties.getUserInfo();
        // private key
        if (this.properties.getPrivateKey() != null) {
            byte[] keyByteArray = FileCopyUtils.copyToByteArray(this.properties.getPrivateKey().getInputStream());
            String passphrase = userInfo != null ? userInfo.getPassphrase() : null;
            if (StringUtils.hasText(passphrase)) {
                passphrase = this.properties.getPrivateKeyPassphrase();
            }
            if (StringUtils.hasText(passphrase)) {
                this.jsch.addIdentity(this.properties.getUsername(), keyByteArray, null, passphrase.getBytes());
            } else {
                this.jsch.addIdentity(this.properties.getUsername(), keyByteArray, null, null);
            }
        }
        Session jschSession = this.jsch.getSession(this.properties.getUsername(), this.properties.getHost(),
                this.properties.getPort());
        jschSession.setConfig("StrictHostKeyChecking", "no");

        if (this.properties.getSession() != null) {
            jschSession.setConfig(this.properties.getSession());
        }
        if (StringUtils.hasText(this.properties.getPassword())) {
            jschSession.setPassword(this.properties.getPassword());
        }
        if (userInfo != null) {
            jschSession.setUserInfo(userInfo);
        }
        try {
            initJschSessionConfig(jschSession);
        } catch (Exception e) {
            throw new FSClientException("Attempt to set additional properties of " +
                    "the com.jcraft.jsch.Session resulted in error: " + e.getMessage(), e);
        }
        return jschSession;
    }



    @Override
    public void destroy() {
        if (this.session != null) {
            this.session.disconnect();
        }
        if (this.jsch != null) {
            this.jsch = null;
        }
    }

    /**
     * 初始化其他配置
     * @param jschSession
     * @throws JSchException
     */
    private void initJschSessionConfig(Session jschSession) throws JSchException {
        if (this.properties.getSessionTimeout() != null) {
            jschSession.setTimeout((int)this.properties.getSessionTimeout().toMillis());
        }
        if (this.properties.getServerAliveInterval() != null) {
            jschSession.setServerAliveInterval((int)this.properties.getServerAliveInterval().toMillis());
        }

        if (StringUtils.hasText(this.properties.getClientVersion())) {
            jschSession.setClientVersion(this.properties.getClientVersion());
        }
        if (StringUtils.hasText(this.properties.getHostKeyAlias())) {
            jschSession.setHostKeyAlias(this.properties.getHostKeyAlias());
        }
        if (this.properties.getServerAliveCountMax() != null) {
            jschSession.setServerAliveCountMax(this.properties.getServerAliveCountMax());
        }
        if (this.properties.getEnableDaemonThread() != null) {
            jschSession.setDaemonThread(this.properties.getEnableDaemonThread());
        }
    }
}
