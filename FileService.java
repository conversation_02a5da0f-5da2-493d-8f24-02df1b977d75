package com.example.fileserver.service;

import com.xmcares.framework.fsclient.FSTemplate;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import com.xmcares.framework.fsclient.minio.MinioFileDesc;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.*;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * 文件服务类
 */
@Service
public class FileService {
    
    private static final Logger logger = LoggerFactory.getLogger(FileService.class);
    
    @Autowired
    private FSTemplate fsTemplate;
    
    /**
     * 上传文件
     * @param file 上传的文件
     * @param targetPath 目标路径
     * @return 文件描述结果
     */
    public FileDescResult uploadFile(MultipartFile file, String targetPath) {
        try {
            // 生成唯一文件名
            String fileName = generateUniqueFileName(file.getOriginalFilename());
            String fullPath = targetPath + "/" + fileName;
            
            // 构建文件属性
            Map<String, String> attributes = new HashMap<>();
            attributes.put("originalName", file.getOriginalFilename());
            attributes.put("contentType", file.getContentType());
            attributes.put("size", String.valueOf(file.getSize()));
            
            // 上传文件
            FileDescResult result = fsTemplate.saveFile(fullPath, file.getInputStream(), attributes);
            
            if (result != null && result.isSuccess()) {
                logger.info("文件上传成功: {}", fullPath);
            } else {
                logger.error("文件上传失败: {}", result != null ? result.getErrorMessage() : "未知错误");
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("文件上传异常: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 使用特定文件描述上传文件（适用于MinIO等需要特殊配置的场景）
     */
    public FileDescResult uploadFileWithDesc(MultipartFile file, String bucket, String objectName) {
        try {
            // 构建MinIO文件描述
            FileDesc fileDesc = MinioFileDesc.builder()
                    .fileName(file.getOriginalFilename())
                    .storeName(objectName)
                    .bucket(bucket)
                    .contentType(file.getContentType())
                    .attribute("originalName", file.getOriginalFilename())
                    .attribute("uploadTime", String.valueOf(System.currentTimeMillis()))
                    .build();
            
            return fsTemplate.saveFile(fileDesc, file.getInputStream());
            
        } catch (Exception e) {
            logger.error("文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("文件上传异常: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 下载文件
     * @param filePath 文件路径
     * @param outputStream 输出流
     * @return 文件描述结果
     */
    public FileDescResult downloadFile(String filePath, OutputStream outputStream) {
        try {
            FileDescResult result = fsTemplate.loadFile(filePath, outputStream);
            
            if (result != null && result.isSuccess()) {
                logger.info("文件下载成功: {}", filePath);
            } else {
                logger.error("文件下载失败: {}", result != null ? result.getErrorMessage() : "未知错误");
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("文件下载异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("文件下载异常: " + e.getMessage())
                    .build();
        }
    }
    
    /**
     * 删除文件
     * @param filePath 文件路径
     * @return 是否删除成功
     */
    public boolean deleteFile(String filePath) {
        try {
            fsTemplate.deleteFile(filePath);
            logger.info("文件删除成功: {}", filePath);
            return true;
            
        } catch (Exception e) {
            logger.error("文件删除异常: {}", filePath, e);
            return false;
        }
    }
    
    /**
     * 获取原始客户端（用于高级操作）
     */
    public Object getRawClient() {
        try {
            return fsTemplate.getRawClient();
        } catch (IOException e) {
            logger.error("获取原始客户端失败", e);
            return null;
        }
    }
    
    /**
     * 生成唯一文件名
     */
    private String generateUniqueFileName(String originalFilename) {
        String extension = "";
        if (originalFilename != null && originalFilename.contains(".")) {
            extension = originalFilename.substring(originalFilename.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }
}
