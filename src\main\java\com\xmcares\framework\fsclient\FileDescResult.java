/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2025/03/18
 */
package com.xmcares.framework.fsclient;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import java.io.IOException;
import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件操作结果类，用于存储文件操作的执行结果
 * <AUTHOR>
 */
public class FileDescResult {

	public static final String ERROR_CODE = "-1";

	// 操作是否成功
	private boolean success;

	// 文件描述对象
	private FileDesc fileDesc;

	// 操作时间
	private LocalDateTime operationTime;

	// 错误码（可选，失败时使用）
	private String errorCode;

	// 错误消息（可选，失败时使用）
	private String errorMessage;

	// 附加信息（例如文件大小、URL 等）
	private Map<String, String> metadata;

	/**
	 * 私有构造方法，强制使用 Builder 创建
	 */
	private FileDescResult() {
		this.metadata = new HashMap<>();
		this.operationTime = LocalDateTime.now();
	}

	// Getter 和 Setter
	public boolean isSuccess() {
		return success;
	}

	public void setSuccess(boolean success) {
		this.success = success;
	}

	public FileDesc getFileDesc() {
		return fileDesc;
	}

	public void setFileDesc(FileDesc fileDesc) {
		this.fileDesc = fileDesc;
	}

	public LocalDateTime getOperationTime() {
		return operationTime;
	}

	public void setOperationTime(LocalDateTime operationTime) {
		this.operationTime = operationTime;
	}

	public String getErrorCode() {
		return errorCode;
	}

	public void setErrorCode(String errorCode) {
		this.errorCode = errorCode;
	}

	public String getErrorMessage() {
		return errorMessage;
	}

	public void setErrorMessage(String errorMessage) {
		this.errorMessage = errorMessage;
	}

	public Map<String, String> getMetadata() {
		return metadata;
	}

	public void setMetadata(Map<String, String> metadata) {
		if (metadata != null) {
			this.metadata = new HashMap<>(metadata);
		}
	}

	public String getMetadataValue(String key) {
		return metadata.get(key);
	}

	public void putMetadata(String key, String value) {
		metadata.put(key, value);
	}

	/**
	 * Builder 模式创建实例
	 */
	public static Builder builder() {
		return new Builder();
	}

	@Override
	public String toString() {
		String meta;
		try {
			meta = JacksonJsonUtils.writeObjectAsString(this.metadata);
		} catch (IOException e) {
			meta = "[serialize error]";
		}
		return "FileDescResult{" +
				"success=" + success +
				", fileDesc=" + fileDesc +
				", operationTime=" + operationTime +
				", errorCode='" + errorCode + '\'' +
				", errorMessage='" + errorMessage + '\'' +
				", metadata=" + meta +
				'}';
	}

	/**
	 * Builder 类
	 */
	public static class Builder {
		private boolean success;
		private FileDesc fileDesc;
		private String errorCode;
		private String errorMessage;
		private final Map<String, String> metadata = new HashMap<>();

		public Builder success(boolean success) {
			this.success = success;
			return this;
		}

		public Builder fileDesc(FileDesc fileDesc) {
			this.fileDesc = fileDesc;
			return this;
		}

		public Builder errorCode(String errorCode) {
			this.errorCode = errorCode;
			return this;
		}

		public Builder errorMessage(String errorMessage) {
			this.errorMessage = errorMessage;
			return this;
		}

		public Builder metadata(String key, String value) {
			this.metadata.put(key, value);
			return this;
		}

		public Builder metadata(Map<String, String> metadata) {
			if (metadata != null) {
				this.metadata.putAll(metadata);
			}
			return this;
		}

		public FileDescResult build() {
			FileDescResult result = new FileDescResult();
			result.success = this.success;
			result.fileDesc = this.fileDesc;
			result.errorCode = this.errorCode;
			result.errorMessage = this.errorMessage;
			result.metadata = new HashMap<>(this.metadata);
			return result;
		}
	}

	// 便捷静态方法
	public static FileDescResult success(FileDesc fileDesc) {
		return FileDescResult.builder()
				.success(true)
				.fileDesc(fileDesc)
				.build();
	}

	public static FileDescResult failure(FileDesc fileDesc, String errorCode, String errorMessage) {
		return FileDescResult.builder()
				.success(false)
				.fileDesc(fileDesc)
				.errorCode(errorCode)
				.errorMessage(errorMessage)
				.build();
	}
}
