/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/5
 */
package com.xmcares.framework.fsclient.local;

import com.xmcares.framework.fsclient.FSClientFactory;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

/**
 * 本地文件系统客户端
 * <AUTHOR>
 * @since 1.3.0
 */
public class LocalFSClientFactory implements FSClientFactory<LocalFSClient> {

    private LocalFSProperties properties;

    public LocalFSClientFactory(LocalFSProperties properties) {
        Assert.notNull(properties, "properties不可为空");
        this.properties = properties;
    }

    @Override
    public LocalFSClient createFSClient() {
        LocalFSClient client = new LocalFSClient();
        if (StringUtils.hasText(this.properties.getBaseDir())) {
            client.setBaseDir(this.properties.getBaseDir());
        }
        return client;
    }


}
