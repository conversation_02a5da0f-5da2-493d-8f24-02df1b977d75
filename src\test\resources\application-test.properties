


xcnf.data.fsclient.type=minio
xcnf.data.fsclient.filter.file-type=
xcnf.data.fsclient.filter.filter-class=
xcnf.data.fsclient.filter.max-upload-size=-1

#xcnf.data.fsclient.ftp.host=*************
#xcnf.data.fsclient.ftp.port=21
#xcnf.data.fsclient.ftp.username=tech
#xcnf.data.fsclient.ftp.password=tech@2023
#xcnf.data.fsclient.ftp.pool.maxTotal=10
#xcnf.data.fsclient.ftp.pool.jmx-enabled=false
#xcnf.data.fsclient.ftp.clientMode=2

xcnf.data.fsclient.minio.endpoint=http://*************:9000
xcnf.data.fsclient.minio.access-key=minioadmin
xcnf.data.fsclient.minio.secret-key=minioadmin
xcnf.data.fsclient.minio.default-bucket=test-bucket



#fsclient.fdfs.tracker_servers = ***********:22122,***********:22122,***********:22122
#fsclient.fdfs.connect_timeout_in_seconds = 5
#fsclient.fdfs.network_timeout_in_seconds = 30
#fsclient.fdfs.charset = UTF-8
#fsclient.fdfs.http_anti_steal_token = false
#fsclient.fdfs.http_secret_key = FastDFS1234567890
#fsclient.fdfs.http_tracker_http_port = 80

#xcnf.data.fsclient.type=xfs
#xcnf.data.fsclient.xfs.wsdlUrl=http://***********:8085/xmcares-filesystem/fileServer?wsdl
#xcnf.data.fsclient.xfs.httpPolicy.connectionTimeout=30000
#xcnf.data.fsclient.xfs.httpPolicy.receiveTimeout=60000



#fsclient.sftp.host=*************
##fsclient.sftp.port=22
#fsclient.sftp.username=tech
#fsclient.sftp.password=tech
#fsclient.sftp.session-timeout=30s


