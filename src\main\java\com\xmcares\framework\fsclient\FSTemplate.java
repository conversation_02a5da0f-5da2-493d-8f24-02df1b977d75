/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/15
 */
package com.xmcares.framework.fsclient;

import com.xmcares.framework.fsclient.filter.FileUploadInterceptor;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public class FSTemplate {

    private FSClientFactory fsClientFactory;
    private FSClientProperties fsclientProperties;
    private List<FileUploadInterceptor> uploadInterceptors = new ArrayList<>();

    public FSTemplate(FSClientFactory fsClientFactory) {
        this.fsClientFactory = fsClientFactory;
    }

    public FileDescResult saveFile(String filePath, InputStream inputStream) throws Exception {
        return this.saveFile(filePath, inputStream, null);
    }

    /**
     *  如果是ftp服务器，filePath即是文件存储在ftp服务器上的路径+文件名
     * @param filePath 文件名，包括文件路径和名称，示例：/dir1/a.png 或 a.png
     * @param inputStream 文件流
     * @param fileAttributes 文件的附加属性
     * @return
     * @throws Exception
     */
    public FileDescResult saveFile(String filePath, InputStream inputStream, Map<String, String> fileAttributes) throws Exception  {
        FileDesc desc = new FileDesc.FileDescImpl(filePath);
        if (fileAttributes != null) {
            desc.putAttributes(fileAttributes);
        }
        return saveFile(desc, inputStream);
    }

    public FileDescResult saveFile(FileDesc desc, InputStream inputStream) throws Exception  {
        // 执行拦截逻辑
        for (FileUploadInterceptor interceptor : uploadInterceptors) {
            FileDescResult interceptResult = interceptor.preUpload(desc, inputStream);
            if (interceptResult != null) {
                if (!interceptResult.isSuccess()) {
                    return interceptResult; // 拦截器返回失败，直接中止上传
                }
                // 如果拦截器修改了 desc 或 inputStream，可以更新参数
                desc = interceptResult.getFileDesc() != null ? interceptResult.getFileDesc() : desc;
            }
        }
        try (FSClient fsClient = fsClientFactory.createFSClient()) {
            return fsClient.uploadFile(desc, inputStream);
        }
    }

    public FileDescResult loadFile(String filePath, OutputStream outputStream) throws Exception{
        FileDesc fileDesc = new FileDesc.FileDescImpl(filePath);
        return loadFile(fileDesc, outputStream);
    }

    public FileDescResult loadFile(FileDesc fileDesc, OutputStream outputStream) throws Exception{
        try (FSClient fsClient = fsClientFactory.createFSClient()) {
            return fsClient.downloadFile(fileDesc, outputStream);
        }
    }
    
    public void deleteFile(String targetPath) throws Exception{
        FileDesc fileDesc = new FileDesc.FileDescImpl(targetPath);
        try (FSClient fsClient = fsClientFactory.createFSClient()) {
            fsClient.deleteFile(fileDesc);
        }
    }

    public void deleteFile(FileDesc fileDesc) throws Exception{
        try (FSClient fsClient = fsClientFactory.createFSClient()) {
            fsClient.deleteFile(fileDesc);
        }
    }

    /**
     * 返回客户端对象
     * @return
     */
    public Object getRawClient() throws IOException {
        try(FSClient fsClient = this.fsClientFactory.createFSClient()){
            return fsClient.getRawClient();
        }
    }

    public FSType getFSType() {
        return this.fsclientProperties.getType();
    }

    public void setFsClientProperties(FSClientProperties properties) {
        this.fsclientProperties = properties;
    }

    public void setUploadInterceptors(List<FileUploadInterceptor> fileUploadInterceptors) {
        this.uploadInterceptors = fileUploadInterceptors;
    }
}
