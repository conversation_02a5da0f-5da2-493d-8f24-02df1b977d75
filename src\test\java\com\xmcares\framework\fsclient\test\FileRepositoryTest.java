/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/16
 */
package com.xmcares.framework.fsclient.test;

import com.xmcares.framework.fsclient.*;
import com.xmcares.framework.fsclient.ftp.FtpFileDesc;
import com.xmcares.framework.fsclient.minio.MinioFileDesc;
import io.minio.MinioClient;
import org.junit.jupiter.api.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.EnableAutoConfiguration;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;

import java.io.*;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
@SpringBootTest(classes = {FSClientAutoConfiguration.class})
@EnableAutoConfiguration
@ActiveProfiles("test")
public class FileRepositoryTest {

	private static Logger logger = LoggerFactory.getLogger(FileRepositoryTest.class);

	@Autowired
	private FSTemplate FSTemplate;

	/*文件加密*/
	@Autowired
	private CryptoStream cryptoStream;
	FileDesc fileDesc = FtpFileDesc.builder()
			.fileName("D:\\1.properties")
			.storeName("/projectUploadFile/a.properties")
			.build();

	@Test
	public void test_file_upload() {
		//1、Do file upload
		//构建要上传的文件流，MultiPartFile or MuliFile o File to InputStream
		FileInputStream fis = null;
		try {
			fis = new FileInputStream("D:\\1.properties");
//			FileDesc fileDesc = MinioFileDesc.builder().fileName("D:\\1.properties")
//					.storeName("/a/a.properties")
//					.bucket("my-bucket")
//					.region("us-east-1")
//					.attribute("tag.tt", "123")
//					.attribute("tag.ee", "456")
//					.build();
			FileDesc.FileDescImpl descImpl = new FileDesc.FileDescImpl();
			FileDesc.FileDescImpl fileDesc = FileDesc.defaultFileDesc("");

			FileDescResult desc = FSTemplate.saveFile(descImpl, fis);

			System.out.println(desc);
		} catch (Exception e) {
			logger.error(e.getMessage());
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e) {
					logger.error(e.getMessage());
				}
			}
		}

		//2、Do jdbc operation
		//...

	}

	//			FileDescfileDesc = FtpFileDesc.builder()
//					.fileName("D:\\1.properties")
//					.storeName("/projectUploadFile/a.properties")
//					.build();
	@Test
	public void test_file_download() {
		//1、Do file upload
		//构建要上传的文件流，MultiPartFile or MuliFile o File to InputStream
		OutputStream fos = null;
		try {
			fos = new FileOutputStream(new File("D:\\2.properties"));
			FileDesc fileDesc = MinioFileDesc.builder().fileName("D:\\1.properties")
					.storeName("/a/a.properties")
					.bucket("my-bucket")
					.region("us-east-1")
					.attribute("tag.tt", "123")
					.attribute("tag.ee", "456")
					.build();


			FileDescResult desc = FSTemplate.loadFile(fileDesc, fos);
			System.out.println(desc);
		} catch (Exception e) {
			e.printStackTrace();
			//Do yourself business transaction
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					logger.error(e.getMessage());
				}
			}
		}

		//2、Do jdbc operation
		//...
	}
//
//	FileDesc fileDesc = FtpFileDesc.builder()
//			.fileName("D:\\1.properties")
//			.storeName("/projectUploadFile/a.properties")
//			.build();
	@Test
	public void test_file_delete() {
		try {
			FileDesc fileDesc = MinioFileDesc.builder().fileName("D:\\1.properties")
					.storeName("/a/a.properties")
					.bucket("my-bucket")
					.region("us-east-1")
					.attribute("tag.tt", "123")
					.attribute("tag.ee", "456")
					.build();



			FSTemplate.deleteFile(fileDesc);
		} catch (Exception e) {
			throw new RuntimeException(e);
		}
	}


	@Test
	public void test_get_raw_client() throws IOException {
		Object client = FSTemplate.getRawClient();
		MinioClient minioClient = (MinioClient) client;
		System.out.println(minioClient);
	}


	@Test
	public void test_get_fs_type() {
		System.out.println(FSTemplate.getFSType());
	}


	@Test
	public void test_file_upload_in_encryption() throws FileNotFoundException {
		//1、Do file upload
		//构建要上传的文件流，MultiPartFile or MuliFile o File to InputStream
		InputStream fis = null;
		try {
			// 加密文件流
			fis = new FileInputStream(new File("E:\\MvnRepository\\com\\xmcares\\framework\\xcnf-data-fsclient\\2.txt"));

			FileDescResult desc = FSTemplate.saveFile("/remote", fis);

			// or
			// FileDesc desc = FSTemplate.saveFile(new FileDesc("/remote/run1.log", "/remote/run1.log.des"), fis);
			System.out.println(desc.toString());
		} catch (Exception e) {
			e.printStackTrace();
		} finally {
			if (fis != null) {
				try {
					fis.close();
				} catch (IOException e) {
					logger.error(e.getMessage());
				}
			}
		}

		//2、Do jdbc operation
		//...

	}

	@Test
	public void test_file_download_in_decryption() {
		//1、Do file upload
		//构建要上传的文件流，MultiPartFile or MuliFile o File to InputStream
		OutputStream fos = null;
		try {
			// 加密文件流
			//fos = cryptoStream.decrypt(new FileOutputStream(new File("/Users/<USER>/Downloads/download111.log")));
			fos = new FileOutputStream(new File("E:\\MvnRepository\\com\\xmcares\\framework\\xcnf-data-fsclient\\1.png"));
			FileDescResult desc = FSTemplate.loadFile("C:\\Users\\<USER>\\Pictures\\1.png", fos);
			System.out.println(desc.toString());
		} catch (Exception e) {
			e.printStackTrace();
			//Do yourself business transaction
		} finally {
			if (fos != null) {
				try {
					fos.close();
				} catch (IOException e) {
					logger.error(e.getMessage());
				}
			}
		}

		//2、Do jdbc operation
		//...
	}
}
