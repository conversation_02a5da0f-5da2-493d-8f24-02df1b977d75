/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/11/5
 */
package com.xmcares.framework.fsclient.test;

import org.apache.cxf.endpoint.Client;
import org.apache.cxf.jaxws.endpoint.dynamic.JaxWsDynamicClientFactory;
import org.apache.cxf.service.model.BindingOperationInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.xml.namespace.QName;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.util.Arrays;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class WSClientTest {

    private static Logger logger = LoggerFactory.getLogger(WSClientTest.class);

    public static void main(String[] args) throws IOException {

        JaxWsDynamicClientFactory clientFactory = JaxWsDynamicClientFactory.newInstance();
        String wsdlUrl = "http://10.83.3.107:8085/xmcares-filesystem/fileServer?wsdl";
        Client client = clientFactory.createClient(wsdlUrl);
        FileInputStream inputStream = new FileInputStream(new File("/Users/<USER>/Downloads/run.log"));

        byte[] bytes = new byte[inputStream.available()];
        inputStream.read(bytes);


        try {
            QName qName = new QName("http://service.filesystem.ws.xmcares.com/", "upload");
            BindingOperationInfo operation = client.getEndpoint().getEndpointInfo().getBinding().getOperation(qName);
            if (operation.isUnwrappedCapable()) {
                operation = operation.getUnwrappedOperation();
            }
            Object[] results = client.invoke(operation,
                    "uploadDefault", "/a", "a.log", bytes);
            System.out.println(Arrays.toString(results));
        } catch (Exception e) {
            logger.error(e.getMessage());
        }

        /*Object[] results = client.invoke(new QName("http://service.filesystem.ws.xmcares.com/", "download"),
                "uploadDefault", "/test1", "bag1.png");*/

        //FileOutputStream outputStream = new FileOutputStream(new File("/Users/<USER>/Downloads/test1.png"));
        //outputStream.write((byte[]) results[0]);
        System.out.println(">>>>>>");
    }



}
