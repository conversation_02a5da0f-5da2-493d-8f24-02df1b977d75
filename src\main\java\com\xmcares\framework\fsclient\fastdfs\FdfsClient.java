/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/15
 */
package com.xmcares.framework.fsclient.fastdfs;

import com.xmcares.framework.fsclient.FSClient;
import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;
import org.csource.common.MyException;
import org.csource.common.NameValuePair;
import org.csource.fastdfs.StorageClient;

import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class FdfsClient implements FSClient {

    private StorageClient storageClient;

    FdfsClient(StorageClient storageClient) {
        this.storageClient = storageClient;
    }

    @Override
    public FileDescResult uploadFile(FileDesc fileDesc, InputStream inputStream) throws IOException {
        checkState();
        NameValuePair[] metaList = getNameValuePairs(fileDesc);
        int length = inputStream.available();
        byte[] bytes = new byte[length];
        while (inputStream.read(bytes) > 0){
            try {
                String[] result = storageClient.upload_file(bytes, null, metaList);
                fileDesc.setStoreName(result[0]+"/"+result[1]);
            } catch (MyException e) {
                throw new FSClientException("FastFDS上传文件失败", e);
            }
        }
        return null;
    }



    @Override
    public FileDescResult downloadFile(FileDesc fileDesc, OutputStream outputStream) throws IOException {
        checkState();
        String[] storePaths = getRemotePath(fileDesc);
        try {
            NameValuePair[] metas = storageClient.get_metadata(storePaths[0], storePaths[1]);
            setFileDesc(fileDesc, metas);
            byte[] result = storageClient.download_file(storePaths[0], storePaths[1]);
            outputStream.write(result);
        } catch (MyException e) {
            throw new FSClientException("FastFDS下载文件失败", e);
        }
        return null;
    }



    @Override
    public void deleteFile(FileDesc fileDesc) throws IOException {
        checkState();
        String[] storePaths = getRemotePath(fileDesc);
        try {
            int size = storageClient.delete_file(storePaths[0], storePaths[1]);
        } catch (MyException e) {
            throw new FSClientException("FastFDS删除文件失败", e);
        }
    }

    @Override
    public boolean isOpen() {
        return this.storageClient != null
                    && this.storageClient.isConnected();
    }

    @Override
    public Object getRawClient() {
        return this.storageClient;
    }

    @Override
    public void close() throws IOException {
        if (this.storageClient != null && this.storageClient.isConnected()) {
            this.storageClient.close();
        }
        this.storageClient = null;
    }

    private NameValuePair[] getNameValuePairs(FileDesc fileDesc) {
        Map<String, String> metas = new HashMap<>();
        metas.putAll(fileDesc.getAttributes());
        metas.put("fileName", fileDesc.getFileName());
        NameValuePair[] metaList = new NameValuePair[metas.size()];
        int i=0;
        for (Map.Entry<String, String> entry : metas.entrySet()) {
            metaList[i] = new NameValuePair(entry.getKey(), entry.getValue());
        }
        return metaList;
    }

    private void setFileDesc(FileDesc fileDesc, NameValuePair[] nameValuePairs) {
        if (nameValuePairs != null && nameValuePairs.length > 0) {
            for (NameValuePair nameValuePair : nameValuePairs) {
                if (nameValuePair.getName().equals("fileName")) {
                    fileDesc.setFileName(nameValuePair.getValue());
                } else {
                    fileDesc.putAttribute(nameValuePair.getName(), nameValuePair.getValue());
                }
            }
        }
    }

    private String[] getRemotePath(FileDesc fileDesc) {
        String storeName = fileDesc.getStoreName();
        int offset = storeName.indexOf("/");
        String[] storePaths = new String[2];
        if (offset < 0) {
            storePaths[0] = "";
            storePaths[1] = storeName;
        } else {
            storePaths[0] = storeName.substring(0, offset);
            storePaths[1] = storeName.substring(offset+1);
        }
        return storePaths;
    }
}
