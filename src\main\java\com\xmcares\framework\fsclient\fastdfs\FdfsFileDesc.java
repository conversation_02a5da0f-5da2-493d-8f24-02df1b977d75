package com.xmcares.framework.fsclient.fastdfs;

import com.xmcares.framework.commons.util.JacksonJsonUtils;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.ftp.FtpFileDesc;

import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

public class FdfsFileDesc implements FileDesc {

	private String fileName = "";
	private String storeName = "";
	private Map<String, String> attributes = new HashMap<>();

	private FdfsFileDesc() {}

	private FdfsFileDesc(String fileName, String storeName, Map<String, String> attributes) {
		this.fileName = fileName;
		this.storeName = storeName;
		this.attributes = attributes != null ? new HashMap<>(attributes) : new HashMap<>();
	}

	@Override public String getFileName() { return fileName == null ? storeName : fileName; }
	@Override public void setFileName(String fileName) { this.fileName = fileName; }
	@Override public String getStoreName() { return storeName == null || storeName.isEmpty() ? fileName : storeName; }
	@Override public void setStoreName(String storeName) { this.storeName = storeName; }
	@Override public Map<String, String> getAttributes() { return attributes; }
	@Override public String getAttribute(String attrName) { return attributes.get(attrName); }
	@Override public void putAttributes(Map<String, String> attributes) { if (attributes != null) this.attributes.putAll(attributes); }
	@Override public void putAttribute(String name, String value) { attributes.put(name, value); }

	@Override
	public String toString() {
		String attris;
		try {
			attris = JacksonJsonUtils.writeObjectAsString(this.attributes);
		} catch (IOException e) {
			attris = "[serialize error]";
		}
		return "FdfsFileDesc{" +
				"fileName='" + fileName + '\'' +
				", storeName='" + storeName + '\'' +
				", attributes=" + attris +
				'}';
	}

	public static FdfsFileDesc.FdfsFileDescBuilder builder() {
		return new FdfsFileDesc.FdfsFileDescBuilder();
	}

	/**
	 * FTP Builder
	 */
	public static class FdfsFileDescBuilder implements FileDesc.Builder<FdfsFileDesc> {
		private String fileName = "";
		private String storeName = "";
		private final Map<String, String> attributes = new HashMap<>();

		@Override
		public FdfsFileDescBuilder fileName(String fileName) {
			this.fileName = fileName;
			return this;
		}

		@Override
		public FdfsFileDescBuilder storeName(String storeName) {
			this.storeName = storeName;
			return this;
		}

		@Override
		public FdfsFileDescBuilder attribute(String name, String value) {
			attributes.put(name, value);
			return this;
		}

		@Override
		public FdfsFileDescBuilder attributes(Map<String, String> attributes) {
			if (attributes != null) {
				this.attributes.putAll(attributes);
			}
			return this;
		}

		@Override
		public FdfsFileDesc build() {
			return new FdfsFileDesc(fileName, storeName, attributes);
		}
	}
}
