/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/11
 */
package com.xmcares.framework.fsclient.test;

import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.local.LocalFSClient;
import com.xmcares.framework.fsclient.local.LocalFSClientFactory;
import com.xmcares.framework.fsclient.local.LocalFSProperties;
import com.xmcares.framework.fsclient.minio.MinioFileDesc;

import java.io.*;

/**
 * <AUTHOR>
 * @since 1.1.0
 */
public class LocalFSClientTest {


    public static void main(String[] args) throws IOException {


        LocalFSProperties properties = new LocalFSProperties();
        LocalFSClientFactory factory = new LocalFSClientFactory(properties);

        LocalFSClient fsClient = factory.createFSClient();
        for (int i = 0; i < 4; i++) {
            long s = System.currentTimeMillis();
            InputStream input = new FileInputStream(new File("/Users/<USER>/Downloads/config.sample.json"));
//            MinioFileDesc fileDesc = new MinioFileDesc("/dir1/test"+i+".json");
//            fileDesc.putAttribute("x", "["+ i +"]");
//            fileDesc.putAttribute("y", "<"+ i +">");
//            fsClient.uploadFile(fileDesc, input);
            long e = System.currentTimeMillis();
            System.out.println("cost: "+ (e-s));
        }

        FileDesc fileDesc = new FileDesc.FileDescImpl("/dir1/test1.json");
        System.out.printf("=================");

        OutputStream output = new FileOutputStream(new File("/Users/<USER>/Downloads/config.sample1.json"));
        fsClient.downloadFile(fileDesc, output);

    }
}
