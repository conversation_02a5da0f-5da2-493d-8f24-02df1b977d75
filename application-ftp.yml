# FTP文件服务器配置
xcnf:
  data:
    fsclient:
      type: ftp
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # FTP具体配置
      ftp:
        host: *************
        port: 21
        username: ftpuser
        password: ftppass
        # 连接池配置
        pool:
          max-total: 10
          max-idle: 5
          min-idle: 2
          jmx-enabled: false
        # FTP客户端配置
        client-mode: 2  # 0=ACTIVE, 2=PASSIVE
        file-type: 2    # 2=BINARY
        control-encoding: UTF-8
        buffer-size: 2048
        connect-timeout: 30000
        default-timeout: 60000
        data-timeout: 60000
