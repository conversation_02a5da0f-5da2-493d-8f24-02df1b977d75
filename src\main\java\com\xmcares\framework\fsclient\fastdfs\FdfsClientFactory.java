/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/15
 */
package com.xmcares.framework.fsclient.fastdfs;

import com.xmcares.framework.fsclient.FSClientException;
import com.xmcares.framework.fsclient.FSClientFactory;
import org.csource.fastdfs.ClientGlobal;
import org.csource.fastdfs.StorageClient;
import org.csource.fastdfs.TrackerClient;
import org.csource.fastdfs.TrackerServer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;

import java.io.IOException;
import java.util.Properties;

/**
 * <AUTHOR>
 * @since 1.0.0
 */
public class FdfsClientFactory implements FSClientFactory<FdfsClient>, InitializingBean {
    protected static final Logger logger = LoggerFactory.getLogger(FdfsClientFactory.class);

    private TrackerClient trackerClient;
    private Properties properties = new Properties();

    @Override
    public FdfsClient createFSClient() {
        TrackerServer trackerServer = null;
        try {
            trackerServer = trackerClient.getTrackerServer();
        } catch (IOException e) {
            throw new FSClientException("创建FastDFS trackServer失败：", e);
        }
        StorageClient storageClient = new StorageClient(trackerServer);
        return new FdfsClient(storageClient);
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        ClientGlobal.initByProperties(this.properties);
//        logger.info("fastdfs network timeout = {}ms", ClientGlobal.g_network_timeout);
//        logger.info("fastdfs charset = {}", ClientGlobal.g_charset);
//        logger.info("fastdfs http.anti_steal_token = {}, token is {}", ClientGlobal.getG_anti_steal_token(), ClientGlobal.getG_secret_key());
        logger.info(ClientGlobal.configInfo());
        trackerClient = new TrackerClient();
    }

    public void setProperties(Properties properties) {
        Assert.notNull(properties, "FastdfsProperties参数不可为NULL");
        this.properties = properties;
    }
}
