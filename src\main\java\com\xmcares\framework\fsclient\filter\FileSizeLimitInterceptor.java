package com.xmcares.framework.fsclient.filter;

import com.xmcares.framework.fsclient.FSClientProperties;
import com.xmcares.framework.fsclient.FileDesc;
import com.xmcares.framework.fsclient.FileDescResult;

import java.io.IOException;
import java.io.InputStream;

public class FileSizeLimitInterceptor implements FileUploadInterceptor {

	// 默认不启用
	private long maxSizeBytes = -1;

	private FSClientProperties properties;

	public FileSizeLimitInterceptor() {
	}

	@Override
	public FileDescResult preUpload(FileDesc fileDesc, InputStream inputStream) {
		try {
			int available = inputStream.available();
			if (maxSizeBytes == -1 || available <= maxSizeBytes) {
				return FileDescResult.builder()
						.success(true)
						.fileDesc(fileDesc)
						.build();
			} else {
				return FileDescResult.builder()
						.success(false)
						.fileDesc(fileDesc)
						.errorCode("SIZE_EXCEEDED")
						.errorMessage("File size exceeds limit: " + available + " > " + maxSizeBytes)
						.build();
			}
		} catch (IOException e) {
			return FileDescResult.builder()
					.success(false)
					.fileDesc(fileDesc)
					.errorCode("IO_ERROR")
					.errorMessage("Failed to check file size: " + e.getMessage())
					.build();
		}
	}

	public long getMaxSizeBytes() {
		if (null != properties) {
			long maxUploadSize = properties.getFilter().getMaxUploadSize();
			this.maxSizeBytes = maxUploadSize > 0 ? maxUploadSize * 1024 : -1;
		}
		return this.maxSizeBytes;
	}

	@Override
	public void setFsClientProperties(FSClientProperties fsClientProperties) {
		this.properties = fsClientProperties;
		this.maxSizeBytes = getMaxSizeBytes();
	}

}
