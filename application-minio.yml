# MinIO文件服务器配置
xcnf:
  data:
    fsclient:
      type: minio
      # 文件过滤配置
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx  # 允许的文件类型
        max-upload-size: 10485760  # 最大上传大小(10MB)，-1表示不限制
      # 文件加密配置（可选）
      crypto:
        key: "your-encryption-key-here"  # 文件加密密钥
      # MinIO具体配置
      minio:
        endpoint: http://localhost:9000
        access-key: minioadmin
        secret-key: minioadmin
        default-bucket: my-app-bucket

# 应用名称（用作默认bucket名称）
spring:
  application:
    name: my-application
