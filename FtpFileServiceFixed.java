@Service
public class FileClientService {

    private static final Logger logger = LoggerFactory.getLogger(FileClientService.class);
    private static final String JAR_SUFFIX = ".jar";

    @Resource
    private FSTemplate fsTemplate;

    /**
     * 上传文件到FTP服务器
     * 
     * @param file 上传的文件
     * @param fileName 文件名
     * @param uploadFilePath 上传路径（必须以/开头和结尾）
     * @return 上传结果
     * @throws IOException IO异常
     */
    public FileDescResult uploadFile(MultipartFile file, String fileName, String uploadFilePath) throws IOException {
        String finalFileName = fileName.endsWith(JAR_SUFFIX) ? fileName : fileName + JAR_SUFFIX;
        
        try {
            // 1. 规范化路径格式
            String normalizedPath = normalizeFtpPath(uploadFilePath);
            String fullStorePath = normalizedPath + finalFileName;
            
            // 2. 验证文件名和路径
            validateFileName(finalFileName);
            validatePath(normalizedPath);
            
            // 3. 构建FTP文件描述 - 关键修复点
            FileDesc fileDesc = FtpFileDesc.builder()
                    .fileName(finalFileName)  // 只设置文件名，不包含路径
                    .storeName(fullStorePath) // 完整的存储路径
                    .build();
            
            // 注意：FTP不支持attributes，所以不要设置
            
            logger.info("准备上传文件: fileName={}, storeName={}", finalFileName, fullStorePath);
            
            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result != null && result.isSuccess()) {
                logger.info("FTP文件上传成功: {}", fullStorePath);
            } else {
                logger.error("FTP文件上传失败: {}", result != null ? result.getErrorMessage() : "未知错误");
            }
            
            return result;
            
        } catch (Exception e) {
            logger.error("FTP文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("FTP上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 从FTP服务器下载文件
     */
    public FileDescResult downloadFile(String remotePath, OutputStream outputStream) {
        try {
            String normalizedPath = normalizeFtpPath(remotePath);
            
            FileDesc fileDesc = FtpFileDesc.builder()
                    .storeName(normalizedPath)
                    .build();

            FileDescResult result = fsTemplate.loadFile(fileDesc, outputStream);

            if (result != null && result.isSuccess()) {
                logger.info("FTP文件下载成功: {}", normalizedPath);
            }

            return result;

        } catch (Exception e) {
            logger.error("FTP文件下载异常: {}", remotePath, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("FTP下载失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 删除FTP服务器上的文件
     */
    public boolean deleteFile(String remotePath) {
        try {
            String normalizedPath = normalizeFtpPath(remotePath);
            
            FileDesc fileDesc = FtpFileDesc.builder()
                    .storeName(normalizedPath)
                    .build();

            fsTemplate.deleteFile(fileDesc);
            logger.info("FTP文件删除成功: {}", normalizedPath);
            return true;

        } catch (Exception e) {
            logger.error("FTP文件删除失败: {}", remotePath, e);
            return false;
        }
    }

    /**
     * 规范化FTP路径格式
     * 
     * @param path 原始路径
     * @return 规范化后的路径
     */
    private String normalizeFtpPath(String path) {
        if (path == null || path.trim().isEmpty()) {
            return "/";
        }
        
        // 移除Windows路径分隔符，统一使用Unix格式
        String normalized = path.replace("\\", "/");
        
        // 确保以/开头
        if (!normalized.startsWith("/")) {
            normalized = "/" + normalized;
        }
        
        // 如果是目录路径，确保以/结尾
        if (!normalized.endsWith("/") && !normalized.contains(".")) {
            normalized = normalized + "/";
        }
        
        // 移除重复的/
        normalized = normalized.replaceAll("/+", "/");
        
        return normalized;
    }

    /**
     * 验证文件名是否合法
     * 
     * @param fileName 文件名
     */
    private void validateFileName(String fileName) {
        if (fileName == null || fileName.trim().isEmpty()) {
            throw new IllegalArgumentException("文件名不能为空");
        }
        
        // 检查文件名中的非法字符
        String[] illegalChars = {"\\", "/", ":", "*", "?", "\"", "<", ">", "|"};
        for (String illegalChar : illegalChars) {
            if (fileName.contains(illegalChar)) {
                throw new IllegalArgumentException("文件名包含非法字符: " + illegalChar);
            }
        }
        
        // 检查文件名长度
        if (fileName.length() > 255) {
            throw new IllegalArgumentException("文件名过长，最大支持255个字符");
        }
    }

    /**
     * 验证路径是否合法
     * 
     * @param path 路径
     */
    private void validatePath(String path) {
        if (path == null) {
            throw new IllegalArgumentException("路径不能为空");
        }
        
        // 检查路径遍历攻击
        if (path.contains("../") || path.contains("..\\")) {
            throw new IllegalArgumentException("路径包含非法的相对路径引用");
        }
        
        // 检查路径长度
        if (path.length() > 1000) {
            throw new IllegalArgumentException("路径过长");
        }
    }

    /**
     * 创建目录（如果需要的话）
     * 注意：FTP客户端会自动创建目录，但您也可以手动创建
     */
    public boolean createDirectory(String directoryPath) {
        try {
            String normalizedPath = normalizeFtpPath(directoryPath);
            
            // 获取原始FTP客户端
            Object rawClient = fsTemplate.getRawClient();
            if (rawClient instanceof org.apache.commons.net.ftp.FTPClient) {
                org.apache.commons.net.ftp.FTPClient ftpClient = (org.apache.commons.net.ftp.FTPClient) rawClient;
                
                // 分层创建目录
                String[] dirs = normalizedPath.split("/");
                StringBuilder currentPath = new StringBuilder();
                
                for (String dir : dirs) {
                    if (!dir.isEmpty()) {
                        currentPath.append("/").append(dir);
                        ftpClient.makeDirectory(currentPath.toString());
                    }
                }
                
                logger.info("FTP目录创建成功: {}", normalizedPath);
                return true;
            }
        } catch (Exception e) {
            logger.error("FTP目录创建失败: {}", directoryPath, e);
        }
        return false;
    }
}
