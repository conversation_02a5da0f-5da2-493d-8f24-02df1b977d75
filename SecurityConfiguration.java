package com.example.fileserver.config;

import com.xmcares.framework.fsclient.filter.FileSizeLimitInterceptor;
import com.xmcares.framework.fsclient.filter.FileTypeInterceptor;
import com.xmcares.framework.fsclient.filter.FileUploadInterceptor;
import com.xmcares.framework.fsclient.FSTemplate;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Configuration;

import javax.annotation.PostConstruct;
import java.util.Arrays;
import java.util.List;

/**
 * 文件服务安全配置
 */
@Configuration
public class SecurityConfiguration {
    
    @Autowired
    private FSTemplate fsTemplate;
    
    /**
     * 配置文件上传拦截器
     */
    @PostConstruct
    public void configureInterceptors() {
        // 文件类型拦截器
        FileTypeInterceptor typeInterceptor = new FileTypeInterceptor();
        typeInterceptor.setAllowedTypes(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp",  // 图片
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",  // 文档
            "txt", "csv", "xml", "json",  // 文本
            "zip", "rar", "7z"  // 压缩包
        ));
        
        // 文件大小限制拦截器
        FileSizeLimitInterceptor sizeInterceptor = new FileSizeLimitInterceptor();
        sizeInterceptor.setMaxSizeBytes(10 * 1024 * 1024); // 10MB
        
        // 自定义安全拦截器
        FileUploadInterceptor securityInterceptor = new CustomSecurityInterceptor();
        
        // 设置拦截器链
        List<FileUploadInterceptor> interceptors = Arrays.asList(
            typeInterceptor,
            sizeInterceptor,
            securityInterceptor
        );
        
        fsTemplate.setUploadInterceptors(interceptors);
    }
}
