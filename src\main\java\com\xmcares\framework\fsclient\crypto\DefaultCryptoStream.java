/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/16
 */
package com.xmcares.framework.fsclient.crypto;

import com.xmcares.framework.commons.error.CryptoException;
import com.xmcares.framework.fsclient.CryptoStream;
import com.xmcares.framework.fsclient.FSClientException;
import org.springframework.beans.factory.InitializingBean;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.crypto.*;
import javax.crypto.spec.DESKeySpec;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.io.InputStream;
import java.io.OutputStream;
import java.security.InvalidKeyException;
import java.security.Key;
import java.security.KeyPairGenerator;
import java.security.NoSuchAlgorithmException;
import java.security.spec.InvalidKeySpecException;

/**
 * DES加密流
 *
 * @updateBy liucx  DES/CBC/PKCS5Padding 这种加密方式必须传递一个IV值
 * <AUTHOR>
 * @since 1.0.0
 */
public class DefaultCryptoStream implements CryptoStream, InitializingBean {

    public static final String ALGORITHM_DES = "DES/CBC/PKCS5Padding";


    //private String encryptKey = "12345`qwert";
    private static String encryptKey = "12345`789";

    private static Key key;

    public DefaultCryptoStream(String encryptKey) {
        if(StringUtils.hasLength(encryptKey)) {
            this.encryptKey = encryptKey;
        }
        if (this.key == null) {
            this.key = generateKey();
        }
    }

    @Override
    public InputStream encrypt(InputStream inputStream) {
        try {
            // 秘密（对称）密钥(SecretKey继承(key))
            // 根据给定的字节数组构造一个密钥。
            // 生成一个实现指定转换的 Cipher 对象。Cipher对象实际完成加解密操作
            Cipher cipher = Cipher.getInstance(ALGORITHM_DES);
            byte[] iv = new byte[cipher.getBlockSize()];
            IvParameterSpec ivParams = new IvParameterSpec(iv);
            // 用密钥初始化此 cipher
            cipher.init(Cipher.ENCRYPT_MODE, this.key, ivParams);
            return new CipherInputStream(inputStream, cipher);
        } catch (Exception e) {
            throw new FSClientException("加密文件流失败", e);
        }
    }

    @Override
    public OutputStream decrypt(OutputStream outputStream) {
        try {
            // 秘密（对称）密钥(SecretKey继承(key))

            // 根据给定的字节数组构造一个密钥。
            // 生成一个实现指定转换的 Cipher 对象。Cipher对象实际完成加解密操作
            Cipher cipher = Cipher.getInstance(ALGORITHM_DES);
            // 用密钥初始化此 cipher
            byte[] iv = new byte[cipher.getBlockSize()];
            IvParameterSpec ivParams = new IvParameterSpec(iv);
            cipher.init(Cipher.DECRYPT_MODE, this.key, ivParams);
            return new CipherOutputStream(outputStream, cipher);
        } catch (Exception e) {
            throw new FSClientException("解密文件流失败", e);
        }
    }


    /**
     * 生成密钥
     *
     * @throws NoSuchAlgorithmException
     * @throws InvalidKeyException
     * @throws InvalidKeySpecException
     */
    private static Key generateKey() {
		/*KeyGenerator keyGenerator = KeyGenerator.getInstance(ALGORITHM_DES);
		keyGenerator.init(new SecureRandom(encryptKey.getBytes()));
		Key key = keyGenerator.generateKey();*/
        try {
            SecretKeyFactory keyFactory = SecretKeyFactory.getInstance("DES");
            byte[] keyBytes = encryptKey.getBytes();
            int byteLen = keyBytes.length;
            if (byteLen < 24) {
                byte[] tmpKey = new byte[24];
                System.arraycopy(keyBytes, 0, tmpKey, 0, byteLen);
                keyBytes = tmpKey;
            }
            DESKeySpec keySpec = new DESKeySpec(keyBytes);
            return keyFactory.generateSecret(keySpec);
        } catch (NoSuchAlgorithmException | InvalidKeySpecException | InvalidKeyException e) {
            throw new CryptoException("加解密发生异常", e);
        }
    }

    @Override
    public void afterPropertiesSet() throws Exception {
        this.key = this.generateKey();
    }

    public void setEncryptKey(String encryptKey) {
        Assert.hasText(encryptKey, "密钥串不可为空");
        this.encryptKey = encryptKey;
    }
}
