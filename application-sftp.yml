# SFTP文件服务器配置
xcnf:
  data:
    fsclient:
      type: sftp
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # SFTP具体配置
      sftp:
        host: *************
        port: 22
        username: sftpuser
        password: sftppass
        # 会话超时配置
        session-timeout: 30s
        channel-connect-timeout: 10s
        # 服务器存活检测
        server-alive-interval: 60s
        server-alive-count-max: 3
        # 安全配置
        allow-unknown-keys: false
        # 私钥认证（可选，与密码认证二选一）
        # private-key: classpath:ssh/id_rsa
        # private-key-passphrase: your-passphrase
        # known-hosts: classpath:ssh/known_hosts
