/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2022/5/7
 */
package com.xmcares.framework.fsclient;

import org.springframework.boot.context.properties.ConfigurationProperties;

import java.util.ArrayList;
import java.util.List;

/**
 * File System properties
 * <AUTHOR>
 * @since 1.3.0
 */
@ConfigurationProperties(prefix = "xcnf.data.fsclient")
public class FSClientProperties {

    /**
     * 文件加密密钥
     */
    private CryptoProperties crypto = new CryptoProperties();


    private FileFilter filter = new FileFilter();

    private FSType type = FSType.FTP;

    public FSClientProperties() {
    }

    public CryptoProperties getCrypto() {
        return crypto;
    }

    public void setCrypto(CryptoProperties crypto) {
        this.crypto = crypto;
    }

    public FSType getType() {
        return type;
    }

    public void setType(FSType type) {
        this.type = type;
    }

    public FileFilter getFilter() {
        return filter;
    }

    public void setFilter(FileFilter filter) {
        this.filter = filter;
    }

    public static class FileFilter {

        /**
         * 自定义文件上传过滤器
         */
        private List<String> filterClass = new ArrayList<>();
        /**
         * 最大文件上传大小  kb
         */
        private long maxUploadSize = -1;
        /**
         * 文件上传白名单
         */
        private List<String> fileType = new ArrayList<>();

        public List<String> getFilterClass() {
            return filterClass;
        }

        public void setFilterClass(List<String> filterClass) {
            this.filterClass = filterClass;
        }

        public long getMaxUploadSize() {
            return maxUploadSize;
        }

        public void setMaxUploadSize(long maxUploadSize) {
            this.maxUploadSize = maxUploadSize;
        }

        public List<String> getFileType() {
            return fileType;
        }

        public void setFileType(List<String> fileType) {
            this.fileType = fileType;
        }
    }

    class CryptoProperties {
        /**
         * 文件加密密钥
         */
        private String key;

        public String getKey() {
            return key;
        }

        public void setKey(String key) {
            this.key = key;
        }
    }
}
