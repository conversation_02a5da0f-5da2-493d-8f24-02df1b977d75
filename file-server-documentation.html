<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>XCNF文件服务器客户端 - 完整使用指南</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/themes/prism-tomorrow.min.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }

        .container {
            display: flex;
            min-height: 100vh;
        }

        /* 侧边栏样式 */
        .sidebar {
            width: 280px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            position: fixed;
            height: 100vh;
            overflow-y: auto;
            z-index: 1000;
            transition: transform 0.3s ease;
        }

        .sidebar-header {
            padding: 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .sidebar-header h1 {
            font-size: 1.2em;
            margin-bottom: 5px;
        }

        .sidebar-header p {
            font-size: 0.9em;
            opacity: 0.8;
        }

        /* 搜索框 */
        .search-container {
            padding: 15px 20px;
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }

        .search-box {
            width: 100%;
            padding: 10px;
            border: none;
            border-radius: 5px;
            background: rgba(255,255,255,0.1);
            color: white;
            placeholder-color: rgba(255,255,255,0.7);
        }

        .search-box::placeholder {
            color: rgba(255,255,255,0.7);
        }

        /* 导航菜单 */
        .nav-menu {
            padding: 10px 0;
        }

        .nav-item {
            display: block;
            padding: 12px 20px;
            color: white;
            text-decoration: none;
            transition: background-color 0.3s;
            border-left: 3px solid transparent;
        }

        .nav-item:hover {
            background-color: rgba(255,255,255,0.1);
            border-left-color: #fff;
        }

        .nav-item.active {
            background-color: rgba(255,255,255,0.2);
            border-left-color: #fff;
        }

        .nav-item i {
            margin-right: 10px;
            width: 16px;
        }

        /* 主内容区域 */
        .main-content {
            flex: 1;
            margin-left: 280px;
            padding: 20px;
            max-width: calc(100% - 280px);
        }

        /* 内容区域 */
        .content-section {
            background: white;
            border-radius: 10px;
            padding: 30px;
            margin-bottom: 30px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            display: none;
        }

        .content-section.active {
            display: block;
        }

        .content-section h1 {
            color: #2c3e50;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 3px solid #3498db;
        }

        .content-section h2 {
            color: #34495e;
            margin: 25px 0 15px 0;
            padding-left: 10px;
            border-left: 4px solid #3498db;
        }

        .content-section h3 {
            color: #2c3e50;
            margin: 20px 0 10px 0;
        }

        /* 代码块样式 */
        .code-container {
            position: relative;
            margin: 15px 0;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .code-header {
            background: #2d3748;
            color: white;
            padding: 10px 15px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
        }

        .code-title {
            font-weight: 500;
        }

        .code-actions {
            display: flex;
            gap: 10px;
        }

        .btn-copy, .btn-toggle {
            background: #4a5568;
            color: white;
            border: none;
            padding: 5px 10px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8em;
            transition: background-color 0.3s;
        }

        .btn-copy:hover, .btn-toggle:hover {
            background: #2d3748;
        }

        .code-content {
            max-height: 400px;
            overflow-y: auto;
        }

        .code-content.collapsed {
            max-height: 0;
            overflow: hidden;
        }

        pre {
            margin: 0 !important;
            border-radius: 0 !important;
        }

        /* 特性卡片 */
        .feature-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .feature-card {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            transition: transform 0.3s;
        }

        .feature-card:hover {
            transform: translateY(-5px);
        }

        .feature-card i {
            font-size: 2.5em;
            margin-bottom: 15px;
            opacity: 0.9;
        }

        .feature-card h3 {
            margin-bottom: 10px;
            color: white;
        }

        /* 存储类型卡片 */
        .storage-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 20px 0;
        }

        .storage-card {
            background: white;
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s;
            cursor: pointer;
        }

        .storage-card:hover {
            border-color: #3498db;
            transform: translateY(-3px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
        }

        .storage-card i {
            font-size: 2em;
            color: #3498db;
            margin-bottom: 10px;
        }

        /* 警告和提示框 */
        .alert {
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid;
        }

        .alert-info {
            background-color: #d1ecf1;
            border-color: #17a2b8;
            color: #0c5460;
        }

        .alert-warning {
            background-color: #fff3cd;
            border-color: #ffc107;
            color: #856404;
        }

        .alert-danger {
            background-color: #f8d7da;
            border-color: #dc3545;
            color: #721c24;
        }

        .alert-success {
            background-color: #d4edda;
            border-color: #28a745;
            color: #155724;
        }

        /* 表格样式 */
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        th, td {
            padding: 12px 15px;
            text-align: left;
            border-bottom: 1px solid #e9ecef;
        }

        th {
            background: #f8f9fa;
            font-weight: 600;
            color: #2c3e50;
        }

        tr:hover {
            background-color: #f8f9fa;
        }

        /* 返回顶部按钮 */
        .back-to-top {
            position: fixed;
            bottom: 30px;
            right: 30px;
            background: #3498db;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            cursor: pointer;
            display: none;
            z-index: 1000;
            transition: all 0.3s;
        }

        .back-to-top:hover {
            background: #2980b9;
            transform: translateY(-3px);
        }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
            }

            .sidebar.open {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                max-width: 100%;
            }

            .mobile-menu-btn {
                display: block;
                position: fixed;
                top: 20px;
                left: 20px;
                z-index: 1001;
                background: #3498db;
                color: white;
                border: none;
                border-radius: 5px;
                padding: 10px;
                cursor: pointer;
            }
        }

        .mobile-menu-btn {
            display: none;
        }

        /* 加载动画 */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #3498db;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 隐藏类 */
        .hidden {
            display: none !important;
        }

        /* 搜索结果高亮 */
        mark {
            background-color: #fff3cd;
            color: #856404;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: bold;
        }

        /* 代码复制提示 */
        .copy-tooltip {
            position: fixed;
            background: #28a745;
            color: white;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 14px;
            z-index: 10000;
            pointer-events: none;
            opacity: 0;
            transition: opacity 0.3s;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        }

        .copy-tooltip.show {
            opacity: 1;
        }

        /* 滚动条样式 */
        .sidebar::-webkit-scrollbar {
            width: 6px;
        }

        .sidebar::-webkit-scrollbar-track {
            background: rgba(255,255,255,0.1);
        }

        .sidebar::-webkit-scrollbar-thumb {
            background: rgba(255,255,255,0.3);
            border-radius: 3px;
        }

        .sidebar::-webkit-scrollbar-thumb:hover {
            background: rgba(255,255,255,0.5);
        }

        .code-content::-webkit-scrollbar {
            width: 8px;
            height: 8px;
        }

        .code-content::-webkit-scrollbar-track {
            background: #f1f1f1;
        }

        .code-content::-webkit-scrollbar-thumb {
            background: #888;
            border-radius: 4px;
        }

        .code-content::-webkit-scrollbar-thumb:hover {
            background: #555;
        }

        /* 打印样式 */
        @media print {
            .sidebar, .mobile-menu-btn, .back-to-top {
                display: none !important;
            }

            .main-content {
                margin-left: 0 !important;
                max-width: 100% !important;
            }

            .content-section {
                display: block !important;
                page-break-inside: avoid;
            }

            .code-container {
                page-break-inside: avoid;
            }

            .btn-copy, .btn-toggle {
                display: none !important;
            }
        }

        /* 无障碍访问 */
        .nav-item:focus,
        .btn-copy:focus,
        .btn-toggle:focus,
        .search-box:focus {
            outline: 2px solid #fff;
            outline-offset: 2px;
        }

        /* 动画效果 */
        .content-section {
            animation: fadeIn 0.3s ease-in-out;
        }

        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        /* 代码块展开/折叠动画 */
        .code-content {
            transition: max-height 0.3s ease-in-out;
            overflow: hidden;
        }

        /* 响应式表格 */
        @media (max-width: 768px) {
            table {
                font-size: 0.9em;
            }

            th, td {
                padding: 8px 10px;
            }

            .feature-grid,
            .storage-grid {
                grid-template-columns: 1fr;
            }
        }

        /* 深色模式支持 */
        @media (prefers-color-scheme: dark) {
            body {
                background-color: #1a1a1a;
                color: #e0e0e0;
            }

            .content-section {
                background: #2d2d2d;
                color: #e0e0e0;
            }

            .content-section h1,
            .content-section h2,
            .content-section h3 {
                color: #ffffff;
            }

            table {
                background: #2d2d2d;
            }

            th {
                background: #3a3a3a;
                color: #ffffff;
            }

            tr:hover {
                background-color: #3a3a3a;
            }
        }
    </style>
</head>
<body>
    <button class="mobile-menu-btn" onclick="toggleSidebar()">
        <i class="fas fa-bars"></i>
    </button>

    <div class="container">
        <!-- 侧边栏 -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <h1><i class="fas fa-server"></i> XCNF文件服务器</h1>
                <p>统一文件存储客户端框架</p>
            </div>
            
            <div class="search-container">
                <input type="text" class="search-box" placeholder="搜索文档内容..." id="searchInput">
            </div>

            <div class="nav-menu">
                <a href="#overview" class="nav-item active" onclick="showSection('overview')">
                    <i class="fas fa-home"></i> 项目概述
                </a>
                <a href="#features" class="nav-item" onclick="showSection('features')">
                    <i class="fas fa-star"></i> 核心特性
                </a>
                <a href="#requirements" class="nav-item" onclick="showSection('requirements')">
                    <i class="fas fa-cog"></i> 环境要求
                </a>
                <a href="#storage-types" class="nav-item" onclick="showSection('storage-types')">
                    <i class="fas fa-database"></i> 存储类型
                </a>
                <a href="#local-config" class="nav-item" onclick="showSection('local-config')">
                    <i class="fas fa-folder"></i> 本地存储配置
                </a>
                <a href="#ftp-config" class="nav-item" onclick="showSection('ftp-config')">
                    <i class="fas fa-upload"></i> FTP配置
                </a>
                <a href="#sftp-config" class="nav-item" onclick="showSection('sftp-config')">
                    <i class="fas fa-shield-alt"></i> SFTP配置
                </a>
                <a href="#minio-config" class="nav-item" onclick="showSection('minio-config')">
                    <i class="fas fa-cloud"></i> MinIO配置
                </a>
                <a href="#fastdfs-config" class="nav-item" onclick="showSection('fastdfs-config')">
                    <i class="fas fa-network-wired"></i> FastDFS配置
                </a>
                <a href="#xfs-config" class="nav-item" onclick="showSection('xfs-config')">
                    <i class="fas fa-globe"></i> WebService配置
                </a>
                <a href="#spring-integration" class="nav-item" onclick="showSection('spring-integration')">
                    <i class="fas fa-leaf"></i> Spring Boot集成
                </a>
                <a href="#api-reference" class="nav-item" onclick="showSection('api-reference')">
                    <i class="fas fa-book"></i> API参考
                </a>
                <a href="#security" class="nav-item" onclick="showSection('security')">
                    <i class="fas fa-lock"></i> 安全配置
                </a>
                <a href="#troubleshooting" class="nav-item" onclick="showSection('troubleshooting')">
                    <i class="fas fa-tools"></i> 故障排查
                </a>
                <a href="#deployment" class="nav-item" onclick="showSection('deployment')">
                    <i class="fas fa-rocket"></i> 部署指南
                </a>
            </div>
        </nav>

        <!-- 主内容区域 -->
        <main class="main-content">
            <!-- 项目概述 -->
            <section id="overview" class="content-section active">
                <h1><i class="fas fa-home"></i> 项目概述</h1>
                
                <p>XCNF文件服务器客户端是一个基于Spring Boot的统一文件存储框架，提供了对多种文件存储后端的统一访问接口。通过简单的配置切换，您可以在不同的存储系统之间无缝迁移，大大简化了文件管理的复杂性。</p>

                <div class="alert alert-info">
                    <strong><i class="fas fa-info-circle"></i> 重要提示：</strong>
                    本框架采用工厂模式和模板模式设计，提供了高度的可扩展性和易用性。所有存储类型都通过统一的API进行操作，降低了学习成本和维护难度。
                </div>

                <h2>架构设计</h2>
                <p>框架采用分层架构设计：</p>
                <ul>
                    <li><strong>应用层</strong>：提供统一的文件操作API</li>
                    <li><strong>抽象层</strong>：定义通用的文件操作接口</li>
                    <li><strong>实现层</strong>：各种存储后端的具体实现</li>
                    <li><strong>配置层</strong>：Spring Boot自动配置支持</li>
                </ul>

                <h2>版本信息</h2>
                <table>
                    <tr>
                        <th>组件</th>
                        <th>版本</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>xcnf-data-fsclient</td>
                        <td>1.4.6.1-SNAPSHOT</td>
                        <td>核心框架</td>
                    </tr>
                    <tr>
                        <td>Spring Boot</td>
                        <td>2.x+</td>
                        <td>推荐版本</td>
                    </tr>
                    <tr>
                        <td>Java</td>
                        <td>8+</td>
                        <td>最低要求</td>
                    </tr>
                </table>
            </section>

            <!-- 核心特性 -->
            <section id="features" class="content-section">
                <h1><i class="fas fa-star"></i> 核心特性</h1>

                <div class="feature-grid">
                    <div class="feature-card">
                        <i class="fas fa-plug"></i>
                        <h3>统一接口</h3>
                        <p>一套API适配多种存储后端，简化开发复杂度</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-magic"></i>
                        <h3>自动配置</h3>
                        <p>Spring Boot自动配置，开箱即用</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-shield-alt"></i>
                        <h3>安全可靠</h3>
                        <p>内置文件过滤、加密、权限控制机制</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-expand-arrows-alt"></i>
                        <h3>高度可扩展</h3>
                        <p>支持自定义存储实现和拦截器</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-tachometer-alt"></i>
                        <h3>高性能</h3>
                        <p>连接池管理，支持并发操作</p>
                    </div>
                    <div class="feature-card">
                        <i class="fas fa-mobile-alt"></i>
                        <h3>多环境支持</h3>
                        <p>支持开发、测试、生产环境配置</p>
                    </div>
                </div>

                <h2>支持的操作</h2>
                <ul>
                    <li><strong>文件上传</strong>：支持单文件和批量上传</li>
                    <li><strong>文件下载</strong>：支持断点续传和流式下载</li>
                    <li><strong>文件删除</strong>：安全的文件删除操作</li>
                    <li><strong>文件列表</strong>：获取目录文件列表</li>
                    <li><strong>文件信息</strong>：获取文件元数据信息</li>
                    <li><strong>目录操作</strong>：创建、删除目录</li>
                </ul>

                <h2>拦截器支持</h2>
                <p>框架提供了强大的拦截器机制，支持：</p>
                <ul>
                    <li><strong>文件类型过滤</strong>：基于扩展名的白名单/黑名单过滤</li>
                    <li><strong>文件大小限制</strong>：防止超大文件上传</li>
                    <li><strong>安全检查</strong>：文件内容安全扫描</li>
                    <li><strong>自定义拦截器</strong>：支持业务自定义逻辑</li>
                </ul>
            </section>

            <!-- 环境要求 -->
            <section id="requirements" class="content-section">
                <h1><i class="fas fa-cog"></i> 环境要求</h1>

                <h2>基础环境</h2>
                <table>
                    <tr>
                        <th>组件</th>
                        <th>最低版本</th>
                        <th>推荐版本</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>Java</td>
                        <td>8</td>
                        <td>11+</td>
                        <td>JDK或OpenJDK</td>
                    </tr>
                    <tr>
                        <td>Maven</td>
                        <td>3.6.0</td>
                        <td>3.8.0+</td>
                        <td>构建工具</td>
                    </tr>
                    <tr>
                        <td>Spring Boot</td>
                        <td>2.0.0</td>
                        <td>2.7.0+</td>
                        <td>应用框架</td>
                    </tr>
                </table>

                <h2>Maven依赖配置</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">pom.xml - 基础依赖配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-xml">&lt;!-- 核心依赖 --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;com.xmcares.framework&lt;/groupId&gt;
    &lt;artifactId&gt;xcnf-data-fsclient&lt;/artifactId&gt;
    &lt;version&gt;1.4.6.1-SNAPSHOT&lt;/version&gt;
&lt;/dependency&gt;

&lt;!-- Spring Boot Starter --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
    &lt;artifactId&gt;spring-boot-starter&lt;/artifactId&gt;
&lt;/dependency&gt;

&lt;!-- Web支持（如果需要REST API） --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;org.springframework.boot&lt;/groupId&gt;
    &lt;artifactId&gt;spring-boot-starter-web&lt;/artifactId&gt;
&lt;/dependency&gt;

&lt;!-- 可选依赖：根据使用的存储类型添加 --&gt;

&lt;!-- MinIO支持 --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;io.minio&lt;/groupId&gt;
    &lt;artifactId&gt;minio&lt;/artifactId&gt;
    &lt;version&gt;8.4.3&lt;/version&gt;
&lt;/dependency&gt;

&lt;!-- SFTP支持 --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;com.jcraft&lt;/groupId&gt;
    &lt;artifactId&gt;jsch&lt;/artifactId&gt;
    &lt;version&gt;0.1.55&lt;/version&gt;
&lt;/dependency&gt;

&lt;!-- FTP支持 --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;commons-net&lt;/groupId&gt;
    &lt;artifactId&gt;commons-net&lt;/artifactId&gt;
    &lt;version&gt;3.8.0&lt;/version&gt;
&lt;/dependency&gt;

&lt;!-- FastDFS支持 --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;org.csource&lt;/groupId&gt;
    &lt;artifactId&gt;fastdfs-client-java&lt;/artifactId&gt;
    &lt;version&gt;1.29-SNAPSHOT&lt;/version&gt;
&lt;/dependency&gt;

&lt;!-- WebService支持 --&gt;
&lt;dependency&gt;
    &lt;groupId&gt;org.apache.cxf&lt;/groupId&gt;
    &lt;artifactId&gt;cxf-rt-frontend-jaxws&lt;/artifactId&gt;
    &lt;version&gt;3.0.5&lt;/version&gt;
&lt;/dependency&gt;</code></pre>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <strong><i class="fas fa-exclamation-triangle"></i> 注意：</strong>
                    根据您实际使用的存储类型，只需要添加对应的可选依赖。不需要的存储类型依赖可以不添加，以减少项目体积。
                </div>
            </section>

            <!-- 存储类型概览 -->
            <section id="storage-types" class="content-section">
                <h1><i class="fas fa-database"></i> 支持的存储类型</h1>

                <p>XCNF文件服务器客户端支持6种不同的存储后端，每种都有其特定的使用场景和优势：</p>

                <div class="storage-grid">
                    <div class="storage-card" onclick="showSection('local-config')">
                        <i class="fas fa-folder"></i>
                        <h3>本地存储 (LOCAL)</h3>
                        <p>适用于开发测试环境，简单快速</p>
                        <small>推荐用于：开发、测试、小型应用</small>
                    </div>
                    <div class="storage-card" onclick="showSection('ftp-config')">
                        <i class="fas fa-upload"></i>
                        <h3>FTP服务器</h3>
                        <p>传统文件传输协议，广泛支持</p>
                        <small>推荐用于：传统系统集成</small>
                    </div>
                    <div class="storage-card" onclick="showSection('sftp-config')">
                        <i class="fas fa-shield-alt"></i>
                        <h3>SFTP服务器</h3>
                        <p>安全的文件传输协议，基于SSH</p>
                        <small>推荐用于：安全要求高的环境</small>
                    </div>
                    <div class="storage-card" onclick="showSection('minio-config')">
                        <i class="fas fa-cloud"></i>
                        <h3>MinIO对象存储</h3>
                        <p>高性能对象存储，S3兼容</p>
                        <small>推荐用于：云原生应用</small>
                    </div>
                    <div class="storage-card" onclick="showSection('fastdfs-config')">
                        <i class="fas fa-network-wired"></i>
                        <h3>FastDFS</h3>
                        <p>分布式文件系统，高可用</p>
                        <small>推荐用于：大规模分布式系统</small>
                    </div>
                    <div class="storage-card" onclick="showSection('xfs-config')">
                        <i class="fas fa-globe"></i>
                        <h3>WebService (XFS)</h3>
                        <p>基于SOAP的文件服务</p>
                        <small>推荐用于：企业级集成</small>
                    </div>
                </div>

                <h2>选择指南</h2>
                <table>
                    <tr>
                        <th>存储类型</th>
                        <th>性能</th>
                        <th>可扩展性</th>
                        <th>安全性</th>
                        <th>复杂度</th>
                        <th>适用场景</th>
                    </tr>
                    <tr>
                        <td>LOCAL</td>
                        <td>高</td>
                        <td>低</td>
                        <td>中</td>
                        <td>低</td>
                        <td>开发测试</td>
                    </tr>
                    <tr>
                        <td>FTP</td>
                        <td>中</td>
                        <td>中</td>
                        <td>低</td>
                        <td>低</td>
                        <td>传统系统</td>
                    </tr>
                    <tr>
                        <td>SFTP</td>
                        <td>中</td>
                        <td>中</td>
                        <td>高</td>
                        <td>中</td>
                        <td>安全传输</td>
                    </tr>
                    <tr>
                        <td>MinIO</td>
                        <td>高</td>
                        <td>高</td>
                        <td>高</td>
                        <td>中</td>
                        <td>云原生</td>
                    </tr>
                    <tr>
                        <td>FastDFS</td>
                        <td>高</td>
                        <td>高</td>
                        <td>中</td>
                        <td>高</td>
                        <td>大规模系统</td>
                    </tr>
                    <tr>
                        <td>XFS</td>
                        <td>中</td>
                        <td>中</td>
                        <td>中</td>
                        <td>中</td>
                        <td>企业集成</td>
                    </tr>
                </table>
            </section>

            <!-- 本地存储配置 -->
            <section id="local-config" class="content-section">
                <h1><i class="fas fa-folder"></i> 本地存储配置</h1>

                <p>本地存储是最简单的存储方式，适用于开发测试环境或小型应用。文件直接存储在服务器的本地文件系统中。</p>

                <h2>配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - 本地存储配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: local
      # 文件过滤配置
      filter:
        file-type: jpg,png,gif,pdf,doc,docx,xls,xlsx,txt
        max-upload-size: 10485760  # 10MB
      # 文件加密配置（可选）
      crypto:
        key: "your-encryption-key-here"
      # 本地存储配置
      local:
        base-dir: /var/app/files  # 文件存储基础目录</code></pre>
                    </div>
                </div>

                <h2>Java代码示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">LocalFileService.java - 本地存储服务</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class LocalFileService {

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 上传文件到本地存储
     */
    public FileDescResult uploadFile(MultipartFile file, String directory) {
        try {
            // 生成文件路径
            String fileName = generateFileName(file.getOriginalFilename());
            String filePath = directory + "/" + fileName;

            // 构建文件属性
            Map&lt;String, String&gt; attributes = new HashMap&lt;&gt;();
            attributes.put("originalName", file.getOriginalFilename());
            attributes.put("contentType", file.getContentType());
            attributes.put("uploadTime", String.valueOf(System.currentTimeMillis()));

            // 上传文件
            FileDescResult result = fsTemplate.saveFile(filePath, file.getInputStream(), attributes);

            if (result.isSuccess()) {
                logger.info("文件上传成功: {}", filePath);
            }

            return result;

        } catch (Exception e) {
            logger.error("文件上传失败", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 下载文件
     */
    public void downloadFile(String filePath, HttpServletResponse response) {
        try {
            // 设置响应头
            String fileName = Paths.get(filePath).getFileName().toString();
            response.setContentType("application/octet-stream");
            response.setHeader("Content-Disposition",
                             "attachment; filename=\"" + fileName + "\"");

            // 下载文件
            fsTemplate.loadFile(filePath, response.getOutputStream());

        } catch (Exception e) {
            logger.error("文件下载失败: {}", filePath, e);
            response.setStatus(HttpStatus.NOT_FOUND.value());
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String filePath) {
        try {
            fsTemplate.deleteFile(filePath);
            logger.info("文件删除成功: {}", filePath);
            return true;
        } catch (Exception e) {
            logger.error("文件删除失败: {}", filePath, e);
            return false;
        }
    }

    private String generateFileName(String originalName) {
        String extension = "";
        if (originalName != null && originalName.contains(".")) {
            extension = originalName.substring(originalName.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }
}</code></pre>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong><i class="fas fa-info-circle"></i> 提示：</strong>
                    本地存储的base-dir目录需要确保应用有读写权限。在生产环境中，建议使用专门的数据目录，并配置适当的备份策略。
                </div>

                <h2>目录结构建议</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">推荐的目录结构</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-text">/var/app/files/
├── uploads/          # 用户上传文件
│   ├── images/       # 图片文件
│   ├── documents/    # 文档文件
│   └── others/       # 其他文件
├── temp/             # 临时文件
├── backup/           # 备份文件
└── logs/             # 操作日志</code></pre>
                    </div>
                </div>
            </section>

            <!-- FTP配置 -->
            <section id="ftp-config" class="content-section">
                <h1><i class="fas fa-upload"></i> FTP服务器配置</h1>

                <p>FTP (File Transfer Protocol) 是一种标准的网络协议，用于在客户端和服务器之间传输文件。虽然安全性相对较低，但兼容性好，广泛支持。</p>

                <h2>配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - FTP配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: ftp
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # FTP具体配置
      ftp:
        host: *************
        port: 21
        username: ftpuser
        password: ftppass
        # 连接池配置
        pool:
          max-total: 10
          max-idle: 5
          min-idle: 2
          jmx-enabled: false
        # FTP客户端配置
        client-mode: 2  # 0=ACTIVE, 2=PASSIVE
        file-type: 2    # 2=BINARY
        control-encoding: UTF-8
        buffer-size: 2048
        connect-timeout: 30000
        default-timeout: 60000
        data-timeout: 60000</code></pre>
                    </div>
                </div>

                <h2>Java代码示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FtpFileService.java - FTP文件服务</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class FtpFileService {

    private static final Logger logger = LoggerFactory.getLogger(FtpFileService.class);

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 上传文件到FTP服务器
     */
    public FileDescResult uploadFile(MultipartFile file, String remotePath) {
        try {
            // 构建FTP文件描述
            FileDesc fileDesc = FtpFileDesc.builder()
                    .fileName(file.getOriginalFilename())
                    .storeName(remotePath)
                    .attribute("contentType", file.getContentType())
                    .attribute("size", String.valueOf(file.getSize()))
                    .build();

            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result.isSuccess()) {
                logger.info("FTP文件上传成功: {}", remotePath);
            } else {
                logger.error("FTP文件上传失败: {}", result.getErrorMessage());
            }

            return result;

        } catch (Exception e) {
            logger.error("FTP文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("FTP上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 从FTP服务器下载文件
     */
    public FileDescResult downloadFile(String remotePath, OutputStream outputStream) {
        try {
            FileDesc fileDesc = FtpFileDesc.builder()
                    .storeName(remotePath)
                    .build();

            FileDescResult result = fsTemplate.loadFile(fileDesc, outputStream);

            if (result.isSuccess()) {
                logger.info("FTP文件下载成功: {}", remotePath);
            }

            return result;

        } catch (Exception e) {
            logger.error("FTP文件下载异常: {}", remotePath, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("FTP下载失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 删除FTP服务器上的文件
     */
    public boolean deleteFile(String remotePath) {
        try {
            FileDesc fileDesc = FtpFileDesc.builder()
                    .storeName(remotePath)
                    .build();

            fsTemplate.deleteFile(fileDesc);
            logger.info("FTP文件删除成功: {}", remotePath);
            return true;

        } catch (Exception e) {
            logger.error("FTP文件删除失败: {}", remotePath, e);
            return false;
        }
    }
}</code></pre>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <strong><i class="fas fa-exclamation-triangle"></i> 安全提醒：</strong>
                    FTP协议传输数据时不加密，包括用户名和密码。在生产环境中，建议使用SFTP或配置FTP over SSL/TLS。
                </div>

                <h2>常见问题</h2>
                <h3>被动模式 vs 主动模式</h3>
                <ul>
                    <li><strong>主动模式 (ACTIVE)</strong>：服务器主动连接客户端，可能被防火墙阻止</li>
                    <li><strong>被动模式 (PASSIVE)</strong>：客户端主动连接服务器，推荐使用</li>
                </ul>

                <h3>防火墙配置</h3>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">防火墙规则示例</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-bash"># 允许FTP控制端口
sudo ufw allow 21/tcp

# 被动模式端口范围（需要在FTP服务器配置）
sudo ufw allow 50000:51000/tcp</code></pre>
                    </div>
                </div>
            </section>

            <!-- SFTP配置 -->
            <section id="sftp-config" class="content-section">
                <h1><i class="fas fa-shield-alt"></i> SFTP服务器配置</h1>

                <p>SFTP (SSH File Transfer Protocol) 是基于SSH协议的安全文件传输协议，提供了加密的数据传输，是FTP的安全替代方案。</p>

                <h2>配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - SFTP配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: sftp
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # SFTP具体配置
      sftp:
        host: *************
        port: 22
        username: sftpuser
        password: sftppass
        # 会话超时配置
        session-timeout: 30s
        channel-connect-timeout: 10s
        # 服务器存活检测
        server-alive-interval: 60s
        server-alive-count-max: 3
        # 安全配置
        allow-unknown-keys: false
        # 私钥认证（可选，与密码认证二选一）
        # private-key: classpath:ssh/id_rsa
        # private-key-passphrase: your-passphrase
        # known-hosts: classpath:ssh/known_hosts</code></pre>
                    </div>
                </div>

                <h2>私钥认证配置</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - 私钥认证配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: sftp
      sftp:
        host: sftp.example.com
        port: 22
        username: sftpuser
        # 使用私钥认证，注释掉密码
        # password: sftppass
        private-key: classpath:ssh/id_rsa
        private-key-passphrase: your-key-passphrase
        known-hosts: classpath:ssh/known_hosts
        allow-unknown-keys: false  # 严格主机密钥验证</code></pre>
                    </div>
                </div>

                <h2>Java代码示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">SftpFileService.java - SFTP文件服务</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class SftpFileService {

    private static final Logger logger = LoggerFactory.getLogger(SftpFileService.class);

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 上传文件到SFTP服务器
     */
    public FileDescResult uploadFile(MultipartFile file, String remotePath) {
        try {
            // 构建SFTP文件描述
            FileDesc fileDesc = SftpFileDesc.builder()
                    .fileName(file.getOriginalFilename())
                    .storeName(remotePath)
                    .attribute("contentType", file.getContentType())
                    .attribute("uploadTime", String.valueOf(System.currentTimeMillis()))
                    .build();

            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result.isSuccess()) {
                logger.info("SFTP文件上传成功: {}", remotePath);
            }

            return result;

        } catch (Exception e) {
            logger.error("SFTP文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("SFTP上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 创建远程目录
     */
    public boolean createDirectory(String remotePath) {
        try {
            // 获取原始SFTP客户端
            Object rawClient = fsTemplate.getRawClient();
            if (rawClient instanceof SftpClient) {
                SftpClient sftpClient = (SftpClient) rawClient;
                // 这里可以调用原始客户端的方法创建目录
                logger.info("SFTP目录创建成功: {}", remotePath);
                return true;
            }
        } catch (Exception e) {
            logger.error("SFTP目录创建失败: {}", remotePath, e);
        }
        return false;
    }

    /**
     * 检查文件是否存在
     */
    public boolean fileExists(String remotePath) {
        try {
            ByteArrayOutputStream testStream = new ByteArrayOutputStream();
            FileDescResult result = downloadFile(remotePath, testStream);
            return result.isSuccess();
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 下载文件
     */
    public FileDescResult downloadFile(String remotePath, OutputStream outputStream) {
        try {
            FileDesc fileDesc = SftpFileDesc.builder()
                    .storeName(remotePath)
                    .build();

            return fsTemplate.loadFile(fileDesc, outputStream);

        } catch (Exception e) {
            logger.error("SFTP文件下载异常: {}", remotePath, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("SFTP下载失败: " + e.getMessage())
                    .build();
        }
    }
}</code></pre>
                    </div>
                </div>

                <div class="alert alert-success">
                    <strong><i class="fas fa-check-circle"></i> 安全优势：</strong>
                    SFTP提供端到端加密，包括认证和数据传输过程，是企业级应用的推荐选择。
                </div>

                <h2>SSH密钥生成</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">生成SSH密钥对</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-bash"># 生成RSA密钥对
ssh-keygen -t rsa -b 4096 -f ~/.ssh/sftp_key

# 将公钥添加到服务器
ssh-copy-id -i ~/.ssh/sftp_key.pub user@sftp-server

# 测试连接
ssh -i ~/.ssh/sftp_key user@sftp-server</code></pre>
                    </div>
                </div>
            </section>

            <!-- MinIO配置 -->
            <section id="minio-config" class="content-section">
                <h1><i class="fas fa-cloud"></i> MinIO对象存储配置</h1>

                <p>MinIO是一个高性能的对象存储服务，兼容Amazon S3 API。它是云原生应用的理想选择，支持分布式部署和高可用性。</p>

                <h2>配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - MinIO配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: minio
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # MinIO具体配置
      minio:
        endpoint: http://localhost:9000
        access-key: minioadmin
        secret-key: minioadmin
        default-bucket: my-app-bucket

# 应用名称（用作默认bucket名称）
spring:
  application:
    name: my-application</code></pre>
                    </div>
                </div>

                <h2>Java代码示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">MinioFileService.java - MinIO文件服务</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class MinioFileService {

    private static final Logger logger = LoggerFactory.getLogger(MinioFileService.class);

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 上传文件到MinIO
     */
    public FileDescResult uploadFile(MultipartFile file, String bucket, String objectName) {
        try {
            // 构建MinIO文件描述
            FileDesc fileDesc = MinioFileDesc.builder()
                    .fileName(file.getOriginalFilename())
                    .storeName(objectName)
                    .bucket(bucket)
                    .contentType(file.getContentType())
                    .attribute("originalName", file.getOriginalFilename())
                    .attribute("uploadTime", String.valueOf(System.currentTimeMillis()))
                    .attribute("size", String.valueOf(file.getSize()))
                    // 设置对象标签
                    .attribute(FileDesc.ATTR_TAG_PREFIX + "category", "upload")
                    .attribute(FileDesc.ATTR_TAG_PREFIX + "source", "web")
                    // 设置元数据
                    .attribute(FileDesc.ATTR_METADATA_PREFIX + "uploader", "system")
                    .build();

            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result.isSuccess()) {
                logger.info("MinIO文件上传成功: bucket={}, object={}", bucket, objectName);
            }

            return result;

        } catch (Exception e) {
            logger.error("MinIO文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("MinIO上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 生成预签名URL用于直接上传
     */
    public String generatePresignedUploadUrl(String bucket, String objectName, int expireSeconds) {
        try {
            // 获取原始MinIO客户端
            Object rawClient = fsTemplate.getRawClient();
            if (rawClient instanceof MinioClient) {
                MinioClient minioClient = (MinioClient) rawClient;
                // 这里可以调用原始客户端生成预签名URL
                // 注意：需要根据实际的MinioClient实现调整
                logger.info("生成预签名URL: bucket={}, object={}", bucket, objectName);
                return "presigned-url-here";
            }
        } catch (Exception e) {
            logger.error("生成预签名URL失败", e);
        }
        return null;
    }

    /**
     * 下载文件
     */
    public FileDescResult downloadFile(String bucket, String objectName, OutputStream outputStream) {
        try {
            FileDesc fileDesc = MinioFileDesc.builder()
                    .storeName(objectName)
                    .bucket(bucket)
                    .build();

            FileDescResult result = fsTemplate.loadFile(fileDesc, outputStream);

            if (result.isSuccess()) {
                logger.info("MinIO文件下载成功: bucket={}, object={}", bucket, objectName);
            }

            return result;

        } catch (Exception e) {
            logger.error("MinIO文件下载异常: bucket={}, object={}", bucket, objectName, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("MinIO下载失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String bucket, String objectName) {
        try {
            FileDesc fileDesc = MinioFileDesc.builder()
                    .storeName(objectName)
                    .bucket(bucket)
                    .build();

            fsTemplate.deleteFile(fileDesc);
            logger.info("MinIO文件删除成功: bucket={}, object={}", bucket, objectName);
            return true;

        } catch (Exception e) {
            logger.error("MinIO文件删除失败: bucket={}, object={}", bucket, objectName, e);
            return false;
        }
    }

    /**
     * 复制文件
     */
    public boolean copyFile(String sourceBucket, String sourceObject,
                           String targetBucket, String targetObject) {
        try {
            // 先下载源文件
            ByteArrayOutputStream sourceStream = new ByteArrayOutputStream();
            FileDescResult downloadResult = downloadFile(sourceBucket, sourceObject, sourceStream);

            if (downloadResult.isSuccess()) {
                // 再上传到目标位置
                ByteArrayInputStream targetStream = new ByteArrayInputStream(sourceStream.toByteArray());

                FileDesc targetDesc = MinioFileDesc.builder()
                        .storeName(targetObject)
                        .bucket(targetBucket)
                        .build();

                FileDescResult uploadResult = fsTemplate.saveFile(targetDesc, targetStream);

                if (uploadResult.isSuccess()) {
                    logger.info("MinIO文件复制成功: {}/{} -> {}/{}",
                               sourceBucket, sourceObject, targetBucket, targetObject);
                    return true;
                }
            }

        } catch (Exception e) {
            logger.error("MinIO文件复制失败", e);
        }
        return false;
    }
}</code></pre>
                    </div>
                </div>

                <h2>MinIO服务器部署</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">docker-compose.yml - MinIO部署</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">version: '3.8'

services:
  minio:
    image: minio/minio:latest
    container_name: minio-server
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3

volumes:
  minio_data:</code></pre>
                    </div>
                </div>

                <div class="alert alert-info">
                    <strong><i class="fas fa-info-circle"></i> 提示：</strong>
                    MinIO支持分布式部署，可以通过多个节点提供高可用性和数据冗余。在生产环境中，建议配置SSL/TLS加密。
                </div>
            </section>

            <!-- FastDFS配置 -->
            <section id="fastdfs-config" class="content-section">
                <h1><i class="fas fa-network-wired"></i> FastDFS分布式文件系统配置</h1>

                <p>FastDFS是一个开源的分布式文件系统，特别适合以文件为载体的在线服务，如相册网站、视频网站等。它具有高性能、高可靠性、无中心、免维护等特点。</p>

                <h2>配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - FastDFS配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: fastdfs
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # FastDFS具体配置
      fdfs:
        tracker_servers: *************:22122,192.168.1.101:22122
        connect_timeout_in_seconds: 5
        network_timeout_in_seconds: 30
        charset: UTF-8
        http_anti_steal_token: false
        http_secret_key: FastDFS1234567890
        http_tracker_http_port: 80</code></pre>
                    </div>
                </div>

                <h2>Java代码示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FastDfsFileService.java - FastDFS文件服务</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class FastDfsFileService {

    private static final Logger logger = LoggerFactory.getLogger(FastDfsFileService.class);

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 上传文件到FastDFS
     */
    public FileDescResult uploadFile(MultipartFile file) {
        try {
            // 构建文件描述
            FileDesc fileDesc = new FileDesc.FileDescImpl();
            fileDesc.setFileName(file.getOriginalFilename());

            // 添加文件元数据
            Map&lt;String, String&gt; attributes = new HashMap&lt;&gt;();
            attributes.put("originalName", file.getOriginalFilename());
            attributes.put("contentType", file.getContentType());
            attributes.put("size", String.valueOf(file.getSize()));
            attributes.put("uploadTime", String.valueOf(System.currentTimeMillis()));
            fileDesc.putAttributes(attributes);

            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result.isSuccess()) {
                logger.info("FastDFS文件上传成功: {}", result.getFileDesc().getStoreName());
            }

            return result;

        } catch (Exception e) {
            logger.error("FastDFS文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("FastDFS上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 根据文件ID下载文件
     */
    public FileDescResult downloadFile(String fileId, OutputStream outputStream) {
        try {
            FileDesc fileDesc = new FileDesc.FileDescImpl();
            fileDesc.setStoreName(fileId);

            FileDescResult result = fsTemplate.loadFile(fileDesc, outputStream);

            if (result.isSuccess()) {
                logger.info("FastDFS文件下载成功: {}", fileId);
            }

            return result;

        } catch (Exception e) {
            logger.error("FastDFS文件下载异常: {}", fileId, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("FastDFS下载失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String fileId) {
        try {
            FileDesc fileDesc = new FileDesc.FileDescImpl();
            fileDesc.setStoreName(fileId);

            fsTemplate.deleteFile(fileDesc);
            logger.info("FastDFS文件删除成功: {}", fileId);
            return true;

        } catch (Exception e) {
            logger.error("FastDFS文件删除失败: {}", fileId, e);
            return false;
        }
    }

    /**
     * 获取文件访问URL
     */
    public String getFileUrl(String fileId) {
        // FastDFS文件ID格式：group1/M00/00/00/wKgBaFxxx.jpg
        // 可以根据配置的HTTP服务器地址构建完整URL
        String httpServer = "http://*************:8080/";
        return httpServer + fileId;
    }
}</code></pre>
                    </div>
                </div>

                <h2>FastDFS集群部署</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FastDFS集群架构</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-text">FastDFS集群架构：

Tracker Server (跟踪服务器)
├── *************:22122
└── 192.168.1.101:22122

Storage Group1 (存储组1)
├── 192.168.1.110:23000
└── 192.168.1.111:23000

Storage Group2 (存储组2)
├── 192.168.1.120:23000
└── 192.168.1.121:23000

HTTP Server (HTTP服务器)
├── *************:8080
└── 192.168.1.101:8080</code></pre>
                    </div>
                </div>

                <div class="alert alert-warning">
                    <strong><i class="fas fa-exclamation-triangle"></i> 注意：</strong>
                    FastDFS需要单独部署Tracker和Storage服务器。建议在生产环境中部署多个Tracker服务器以提供高可用性。
                </div>
            </section>

            <!-- WebService配置 -->
            <section id="xfs-config" class="content-section">
                <h1><i class="fas fa-globe"></i> WebService (XFS) 配置</h1>

                <p>XFS (Xmcares File System) 是基于SOAP WebService的文件服务，适用于企业级系统集成。它提供了标准化的Web服务接口，便于与其他系统进行集成。</p>

                <h2>配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - WebService配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      type: xfs
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760
      crypto:
        key: "your-encryption-key-here"
      # XFS WebService配置
      xfs:
        wsdl-url: http://your-server:8080/xmcares-filesystem/fileServer?wsdl
        # HTTP客户端策略配置
        http-policy:
          connection-timeout: 30000
          receive-timeout: 60000</code></pre>
                    </div>
                </div>

                <h2>Java代码示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">XfsFileService.java - WebService文件服务</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class XfsFileService {

    private static final Logger logger = LoggerFactory.getLogger(XfsFileService.class);

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 上传文件到WebService文件服务器
     */
    public FileDescResult uploadFile(MultipartFile file, String remotePath, String catalog) {
        try {
            // 构建XFS文件描述
            FileDesc fileDesc = XfsFileDesc.builder()
                    .fileName(file.getOriginalFilename())
                    .storeName(remotePath)
                    .attribute(XfsClient.WS_CATALOG, catalog)  // 文件类别
                    .attribute("contentType", file.getContentType())
                    .attribute("size", String.valueOf(file.getSize()))
                    .build();

            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result.isSuccess()) {
                logger.info("XFS文件上传成功: path={}, catalog={}", remotePath, catalog);
            }

            return result;

        } catch (Exception e) {
            logger.error("XFS文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("XFS上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 上传加密文件
     */
    public FileDescResult uploadEncryptedFile(MultipartFile file, String remotePath, String catalog) {
        try {
            FileDesc fileDesc = XfsFileDesc.builder()
                    .fileName(file.getOriginalFilename())
                    .storeName(remotePath)
                    .attribute(XfsClient.WS_METHOD, "uploadEncryptFile")  // 指定加密上传方法
                    .attribute(XfsClient.WS_CATALOG, catalog)
                    .build();

            FileDescResult result = fsTemplate.saveFile(fileDesc, file.getInputStream());

            if (result.isSuccess()) {
                logger.info("XFS加密文件上传成功: path={}", remotePath);
            }

            return result;

        } catch (Exception e) {
            logger.error("XFS加密文件上传异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("XFS加密上传失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 下载文件
     */
    public FileDescResult downloadFile(String remotePath, String catalog, OutputStream outputStream) {
        try {
            FileDesc fileDesc = XfsFileDesc.builder()
                    .storeName(remotePath)
                    .attribute(XfsClient.WS_CATALOG, catalog)
                    .build();

            FileDescResult result = fsTemplate.loadFile(fileDesc, outputStream);

            if (result.isSuccess()) {
                logger.info("XFS文件下载成功: path={}", remotePath);
            }

            return result;

        } catch (Exception e) {
            logger.error("XFS文件下载异常: path={}", remotePath, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("XFS下载失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 下载加密文件
     */
    public FileDescResult downloadEncryptedFile(String remotePath, String catalog, OutputStream outputStream) {
        try {
            FileDesc fileDesc = XfsFileDesc.builder()
                    .storeName(remotePath)
                    .attribute(XfsClient.WS_METHOD, "downloadEncryptFile")
                    .attribute(XfsClient.WS_CATALOG, catalog)
                    .build();

            return fsTemplate.loadFile(fileDesc, outputStream);

        } catch (Exception e) {
            logger.error("XFS加密文件下载异常: path={}", remotePath, e);
            return FileDescResult.builder()
                    .success(false)
                    .errorMessage("XFS加密下载失败: " + e.getMessage())
                    .build();
        }
    }

    /**
     * 删除文件
     */
    public boolean deleteFile(String remotePath, String catalog) {
        try {
            FileDesc fileDesc = XfsFileDesc.builder()
                    .storeName(remotePath)
                    .attribute(XfsClient.WS_CATALOG, catalog)
                    .build();

            fsTemplate.deleteFile(fileDesc);
            logger.info("XFS文件删除成功: path={}", remotePath);
            return true;

        } catch (Exception e) {
            logger.error("XFS文件删除失败: path={}", remotePath, e);
            return false;
        }
    }
}</code></pre>
                    </div>
                </div>

                <h2>WebService方法说明</h2>
                <table>
                    <tr>
                        <th>方法名</th>
                        <th>参数</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>upload</td>
                        <td>catalog, filePath, fileName, bytes</td>
                        <td>普通文件上传</td>
                    </tr>
                    <tr>
                        <td>uploadEncryptFile</td>
                        <td>catalog, filePath, fileName, bytes</td>
                        <td>加密文件上传</td>
                    </tr>
                    <tr>
                        <td>download</td>
                        <td>catalog, filePath, fileName</td>
                        <td>普通文件下载</td>
                    </tr>
                    <tr>
                        <td>downloadEncryptFile</td>
                        <td>catalog, filePath, fileName</td>
                        <td>加密文件下载</td>
                    </tr>
                    <tr>
                        <td>delete</td>
                        <td>catalog, filePath, fileName</td>
                        <td>删除文件</td>
                    </tr>
                </table>

                <div class="alert alert-info">
                    <strong><i class="fas fa-info-circle"></i> 提示：</strong>
                    WebService文件服务器需要指定文件类别(catalog)参数，这是业务分类的重要标识。不同的catalog可能对应不同的存储策略。
                </div>
            </section>

            <!-- Spring Boot集成 -->
            <section id="spring-integration" class="content-section">
                <h1><i class="fas fa-leaf"></i> Spring Boot集成</h1>

                <p>XCNF文件服务器客户端提供了完整的Spring Boot自动配置支持，可以通过简单的配置实现开箱即用。</p>

                <h2>自动配置原理</h2>
                <p>框架通过<code>@EnableAutoConfiguration</code>机制自动配置相关Bean：</p>
                <ul>
                    <li><strong>FSClientFactory</strong>：根据配置类型自动创建对应的客户端工厂</li>
                    <li><strong>FSTemplate</strong>：统一的文件操作模板</li>
                    <li><strong>拦截器</strong>：文件上传拦截器链</li>
                    <li><strong>属性配置</strong>：各种存储类型的配置属性</li>
                </ul>

                <h2>完整的Spring Boot应用示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FileServerApplication.java - 主启动类</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@SpringBootApplication
public class FileServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(FileServerApplication.class, args);
    }

    /**
     * 自定义文件上传拦截器配置
     */
    @Bean
    @ConditionalOnMissingBean
    public List&lt;FileUploadInterceptor&gt; fileUploadInterceptors() {
        List&lt;FileUploadInterceptor&gt; interceptors = new ArrayList&lt;&gt;();

        // 文件类型拦截器
        FileTypeInterceptor typeInterceptor = new FileTypeInterceptor();
        typeInterceptor.setAllowedTypes(Arrays.asList(
            "jpg", "jpeg", "png", "gif", "bmp",
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            "txt", "csv", "xml", "json"
        ));
        interceptors.add(typeInterceptor);

        // 文件大小拦截器
        FileSizeLimitInterceptor sizeInterceptor = new FileSizeLimitInterceptor();
        sizeInterceptor.setMaxSizeBytes(10 * 1024 * 1024); // 10MB
        interceptors.add(sizeInterceptor);

        return interceptors;
    }
}</code></pre>
                    </div>
                </div>

                <h2>配置文件管理</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - 多环境配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml"># 通用配置
spring:
  profiles:
    active: dev
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

xcnf:
  data:
    fsclient:
      filter:
        file-type: jpg,png,pdf,doc,docx,xls,xlsx
        max-upload-size: 10485760

---
# 开发环境
spring:
  profiles: dev

xcnf:
  data:
    fsclient:
      type: local
      local:
        base-dir: ./dev-files

---
# 测试环境
spring:
  profiles: test

xcnf:
  data:
    fsclient:
      type: minio
      minio:
        endpoint: http://test-minio:9000
        access-key: testuser
        secret-key: testpass
        default-bucket: test-bucket

---
# 生产环境
spring:
  profiles: prod

xcnf:
  data:
    fsclient:
      type: minio
      minio:
        endpoint: https://prod-minio.example.com
        access-key: ${MINIO_ACCESS_KEY}
        secret-key: ${MINIO_SECRET_KEY}
        default-bucket: prod-bucket</code></pre>
                    </div>
                </div>

                <h2>REST API控制器</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FileController.java - 完整的REST API</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@RestController
@RequestMapping("/api/files")
@Validated
public class FileController {

    @Autowired
    private FSTemplate fsTemplate;

    /**
     * 单文件上传
     */
    @PostMapping("/upload")
    public ResponseEntity&lt;ApiResponse&lt;FileUploadResult&gt;&gt; uploadFile(
            @RequestParam("file") @NotNull MultipartFile file,
            @RequestParam(value = "path", defaultValue = "/uploads") String targetPath) {

        try {
            // 验证文件
            if (file.isEmpty()) {
                return ResponseEntity.badRequest()
                    .body(ApiResponse.error("文件不能为空"));
            }

            // 生成文件路径
            String fileName = generateUniqueFileName(file.getOriginalFilename());
            String fullPath = targetPath + "/" + fileName;

            // 构建文件属性
            Map&lt;String, String&gt; attributes = new HashMap&lt;&gt;();
            attributes.put("originalName", file.getOriginalFilename());
            attributes.put("contentType", file.getContentType());
            attributes.put("size", String.valueOf(file.getSize()));
            attributes.put("uploadTime", String.valueOf(System.currentTimeMillis()));

            // 上传文件
            FileDescResult result = fsTemplate.saveFile(fullPath, file.getInputStream(), attributes);

            if (result.isSuccess()) {
                FileUploadResult uploadResult = FileUploadResult.builder()
                    .fileName(fileName)
                    .originalName(file.getOriginalFilename())
                    .filePath(fullPath)
                    .size(file.getSize())
                    .contentType(file.getContentType())
                    .build();

                return ResponseEntity.ok(ApiResponse.success(uploadResult));
            } else {
                return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body(ApiResponse.error("上传失败: " + result.getErrorMessage()));
            }

        } catch (Exception e) {
            logger.error("文件上传异常", e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("上传异常: " + e.getMessage()));
        }
    }

    /**
     * 批量文件上传
     */
    @PostMapping("/upload/batch")
    public ResponseEntity&lt;ApiResponse&lt;List&lt;FileUploadResult&gt;&gt;&gt; uploadFiles(
            @RequestParam("files") @NotEmpty MultipartFile[] files,
            @RequestParam(value = "path", defaultValue = "/uploads") String targetPath) {

        List&lt;FileUploadResult&gt; results = new ArrayList&lt;&gt;();
        List&lt;String&gt; errors = new ArrayList&lt;&gt;();

        for (MultipartFile file : files) {
            try {
                if (!file.isEmpty()) {
                    String fileName = generateUniqueFileName(file.getOriginalFilename());
                    String fullPath = targetPath + "/" + fileName;

                    FileDescResult result = fsTemplate.saveFile(fullPath, file.getInputStream());

                    if (result.isSuccess()) {
                        results.add(FileUploadResult.builder()
                            .fileName(fileName)
                            .originalName(file.getOriginalFilename())
                            .filePath(fullPath)
                            .size(file.getSize())
                            .build());
                    } else {
                        errors.add(file.getOriginalFilename() + ": " + result.getErrorMessage());
                    }
                }
            } catch (Exception e) {
                errors.add(file.getOriginalFilename() + ": " + e.getMessage());
            }
        }

        if (!errors.isEmpty()) {
            return ResponseEntity.status(HttpStatus.PARTIAL_CONTENT)
                .body(ApiResponse.partialSuccess(results, "部分文件上传失败: " + String.join(", ", errors)));
        }

        return ResponseEntity.ok(ApiResponse.success(results));
    }

    /**
     * 文件下载
     */
    @GetMapping("/download")
    public void downloadFile(
            @RequestParam("path") @NotBlank String filePath,
            @RequestParam(value = "filename", required = false) String downloadFilename,
            HttpServletResponse response) throws IOException {

        try {
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            FileDescResult result = fsTemplate.loadFile(filePath, outputStream);

            if (result.isSuccess()) {
                // 设置响应头
                response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);

                String filename = downloadFilename != null ? downloadFilename :
                                 Paths.get(filePath).getFileName().toString();
                String encodedFilename = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());

                response.setHeader(HttpHeaders.CONTENT_DISPOSITION,
                                 "attachment; filename=\"" + encodedFilename + "\"");

                byte[] fileBytes = outputStream.toByteArray();
                response.setContentLength(fileBytes.length);

                // 写入响应
                response.getOutputStream().write(fileBytes);
                response.getOutputStream().flush();
            } else {
                response.setStatus(HttpStatus.NOT_FOUND.value());
                response.getWriter().write("文件不存在: " + result.getErrorMessage());
            }

        } catch (Exception e) {
            logger.error("文件下载异常: {}", filePath, e);
            response.setStatus(HttpStatus.INTERNAL_SERVER_ERROR.value());
            response.getWriter().write("下载失败: " + e.getMessage());
        }
    }

    /**
     * 删除文件
     */
    @DeleteMapping("/delete")
    public ResponseEntity&lt;ApiResponse&lt;Void&gt;&gt; deleteFile(
            @RequestParam("path") @NotBlank String filePath) {

        try {
            fsTemplate.deleteFile(filePath);
            return ResponseEntity.ok(ApiResponse.success(null, "文件删除成功"));

        } catch (Exception e) {
            logger.error("文件删除异常: {}", filePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("删除失败: " + e.getMessage()));
        }
    }

    /**
     * 获取文件信息
     */
    @GetMapping("/info")
    public ResponseEntity&lt;ApiResponse&lt;FileInfo&gt;&gt; getFileInfo(
            @RequestParam("path") @NotBlank String filePath) {

        try {
            // 尝试下载文件头部来获取信息
            ByteArrayOutputStream testStream = new ByteArrayOutputStream();
            FileDescResult result = fsTemplate.loadFile(filePath, testStream);

            if (result.isSuccess()) {
                FileInfo fileInfo = FileInfo.builder()
                    .filePath(filePath)
                    .size(testStream.size())
                    .exists(true)
                    .lastModified(System.currentTimeMillis())
                    .build();

                return ResponseEntity.ok(ApiResponse.success(fileInfo));
            } else {
                return ResponseEntity.status(HttpStatus.NOT_FOUND)
                    .body(ApiResponse.error("文件不存在"));
            }

        } catch (Exception e) {
            logger.error("获取文件信息异常: {}", filePath, e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                .body(ApiResponse.error("获取信息失败: " + e.getMessage()));
        }
    }

    private String generateUniqueFileName(String originalName) {
        String extension = "";
        if (originalName != null && originalName.contains(".")) {
            extension = originalName.substring(originalName.lastIndexOf("."));
        }
        return UUID.randomUUID().toString() + extension;
    }
}</code></pre>
                    </div>
                </div>

                <h2>响应数据模型</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">ApiResponse.java - 统一响应格式</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ApiResponse&lt;T&gt; {

    private boolean success;
    private String message;
    private T data;
    private String errorCode;
    private Long timestamp;

    public static &lt;T&gt; ApiResponse&lt;T&gt; success(T data) {
        return success(data, "操作成功");
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; success(T data, String message) {
        return ApiResponse.&lt;T&gt;builder()
            .success(true)
            .message(message)
            .data(data)
            .timestamp(System.currentTimeMillis())
            .build();
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; error(String message) {
        return error(message, "OPERATION_FAILED");
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; error(String message, String errorCode) {
        return ApiResponse.&lt;T&gt;builder()
            .success(false)
            .message(message)
            .errorCode(errorCode)
            .timestamp(System.currentTimeMillis())
            .build();
    }

    public static &lt;T&gt; ApiResponse&lt;T&gt; partialSuccess(T data, String message) {
        return ApiResponse.&lt;T&gt;builder()
            .success(true)
            .message(message)
            .data(data)
            .errorCode("PARTIAL_SUCCESS")
            .timestamp(System.currentTimeMillis())
            .build();
    }
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileUploadResult {
    private String fileName;
    private String originalName;
    private String filePath;
    private Long size;
    private String contentType;
    private String url;
}

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class FileInfo {
    private String filePath;
    private Long size;
    private Boolean exists;
    private Long lastModified;
    private String contentType;
    private Map&lt;String, String&gt; attributes;
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- API参考 -->
            <section id="api-reference" class="content-section">
                <h1><i class="fas fa-book"></i> API参考文档</h1>

                <p>本节提供了XCNF文件服务器客户端的完整API参考，包括核心接口、方法签名和使用示例。</p>

                <h2>核心接口</h2>

                <h3>FSClient 接口</h3>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FSClient.java - 核心文件操作接口</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">public interface FSClient&lt;T extends FileDesc&gt; extends Closeable {

    /**
     * 上传文件
     * @param fileDesc 文件描述信息
     * @param inputStream 文件输入流
     * @return 上传结果
     * @throws IOException IO异常
     */
    FileDescResult uploadFile(T fileDesc, InputStream inputStream) throws IOException;

    /**
     * 下载文件
     * @param fileDesc 文件描述信息
     * @param outputStream 文件输出流
     * @return 下载结果
     * @throws IOException IO异常
     */
    FileDescResult downloadFile(T fileDesc, OutputStream outputStream) throws IOException;

    /**
     * 删除文件
     * @param fileDesc 文件描述信息
     * @throws IOException IO异常
     */
    void deleteFile(T fileDesc) throws IOException;

    /**
     * 检查客户端是否打开
     * @return 是否打开
     */
    boolean isOpen();

    /**
     * 获取原始客户端对象
     * @return 原始客户端
     */
    Object getRawClient();
}</code></pre>
                    </div>
                </div>

                <h3>FSTemplate 类</h3>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FSTemplate.java - 文件操作模板类</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">public class FSTemplate {

    /**
     * 保存文件（简单版本）
     * @param filePath 文件路径
     * @param inputStream 输入流
     * @return 保存结果
     */
    public FileDescResult saveFile(String filePath, InputStream inputStream) throws Exception;

    /**
     * 保存文件（带属性）
     * @param filePath 文件路径
     * @param inputStream 输入流
     * @param fileAttributes 文件属性
     * @return 保存结果
     */
    public FileDescResult saveFile(String filePath, InputStream inputStream,
                                  Map&lt;String, String&gt; fileAttributes) throws Exception;

    /**
     * 保存文件（使用文件描述）
     * @param desc 文件描述
     * @param inputStream 输入流
     * @return 保存结果
     */
    public FileDescResult saveFile(FileDesc desc, InputStream inputStream) throws Exception;

    /**
     * 加载文件
     * @param filePath 文件路径
     * @param outputStream 输出流
     * @return 加载结果
     */
    public FileDescResult loadFile(String filePath, OutputStream outputStream) throws Exception;

    /**
     * 加载文件（使用文件描述）
     * @param fileDesc 文件描述
     * @param outputStream 输出流
     * @return 加载结果
     */
    public FileDescResult loadFile(FileDesc fileDesc, OutputStream outputStream) throws Exception;

    /**
     * 删除文件
     * @param targetPath 目标路径
     */
    public void deleteFile(String targetPath) throws Exception;

    /**
     * 删除文件（使用文件描述）
     * @param fileDesc 文件描述
     */
    public void deleteFile(FileDesc fileDesc) throws Exception;

    /**
     * 获取原始客户端
     * @return 原始客户端对象
     */
    public Object getRawClient() throws IOException;

    /**
     * 获取文件系统类型
     * @return 文件系统类型
     */
    public FSType getFSType();
}</code></pre>
                    </div>
                </div>

                <h2>文件描述类</h2>
                <table>
                    <tr>
                        <th>类名</th>
                        <th>适用存储</th>
                        <th>特殊属性</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>FileDesc.FileDescImpl</td>
                        <td>LOCAL, FTP, SFTP, FastDFS</td>
                        <td>无</td>
                        <td>通用文件描述</td>
                    </tr>
                    <tr>
                        <td>MinioFileDesc</td>
                        <td>MinIO</td>
                        <td>bucket, region, contentType</td>
                        <td>MinIO对象描述</td>
                    </tr>
                    <tr>
                        <td>FtpFileDesc</td>
                        <td>FTP</td>
                        <td>无</td>
                        <td>FTP文件描述</td>
                    </tr>
                    <tr>
                        <td>SftpFileDesc</td>
                        <td>SFTP</td>
                        <td>无</td>
                        <td>SFTP文件描述</td>
                    </tr>
                    <tr>
                        <td>XfsFileDesc</td>
                        <td>WebService</td>
                        <td>method, catalog</td>
                        <td>WebService文件描述</td>
                    </tr>
                </table>

                <h2>配置属性</h2>
                <table>
                    <tr>
                        <th>配置项</th>
                        <th>类型</th>
                        <th>默认值</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.type</td>
                        <td>FSType</td>
                        <td>LOCAL</td>
                        <td>文件系统类型</td>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.filter.file-type</td>
                        <td>String</td>
                        <td>空</td>
                        <td>允许的文件类型，逗号分隔</td>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.filter.max-upload-size</td>
                        <td>Long</td>
                        <td>-1</td>
                        <td>最大上传大小，-1表示不限制</td>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.crypto.key</td>
                        <td>String</td>
                        <td>空</td>
                        <td>文件加密密钥</td>
                    </tr>
                </table>

                <h2>异常处理</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">异常类型说明</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">// 主要异常类型
FSClientException - 文件服务客户端异常
├── 连接异常 - 无法连接到文件服务器
├── 认证异常 - 用户名密码错误或权限不足
├── 文件不存在异常 - 目标文件不存在
├── 磁盘空间不足异常 - 服务器磁盘空间不足
├── 网络超时异常 - 网络连接超时
└── 配置错误异常 - 配置参数错误

// 拦截器异常
FileUploadInterceptor.preUpload() 返回 FileDescResult
├── success = false - 拦截器拒绝上传
├── errorCode - 错误代码
└── errorMessage - 错误消息</code></pre>
                    </div>
                </div>
            </section>

            <!-- 安全配置 -->
            <section id="security" class="content-section">
                <h1><i class="fas fa-lock"></i> 安全配置与最佳实践</h1>

                <p>安全是文件服务的重要考虑因素。本节介绍如何配置和使用各种安全功能来保护您的文件系统。</p>

                <h2>文件过滤配置</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">SecurityConfig.java - 安全配置类</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Configuration
public class FileSecurityConfig {

    @Bean
    public FileTypeInterceptor fileTypeInterceptor() {
        FileTypeInterceptor interceptor = new FileTypeInterceptor();

        // 设置允许的文件类型
        List&lt;String&gt; allowedTypes = Arrays.asList(
            // 图片文件
            "jpg", "jpeg", "png", "gif", "bmp", "webp",
            // 文档文件
            "pdf", "doc", "docx", "xls", "xlsx", "ppt", "pptx",
            // 文本文件
            "txt", "csv", "xml", "json",
            // 压缩文件
            "zip", "rar", "7z"
        );
        interceptor.setAllowedTypes(allowedTypes);

        return interceptor;
    }

    @Bean
    public FileSizeLimitInterceptor fileSizeLimitInterceptor() {
        FileSizeLimitInterceptor interceptor = new FileSizeLimitInterceptor();
        interceptor.setMaxSizeBytes(50 * 1024 * 1024); // 50MB
        return interceptor;
    }

    @Bean
    public CustomSecurityInterceptor customSecurityInterceptor() {
        return new CustomSecurityInterceptor();
    }
}</code></pre>
                    </div>
                </div>

                <h2>文件加密配置</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - 加密配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      # 文件加密配置
      crypto:
        key: "${FILE_ENCRYPTION_KEY:default-encryption-key-change-in-production}"
        algorithm: "AES"
        mode: "CBC"
        padding: "PKCS5Padding"</code></pre>
                    </div>
                </div>

                <h2>访问控制</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FileAccessController.java - 访问控制</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Component
public class FileAccessController {

    /**
     * 检查用户是否有文件访问权限
     */
    public boolean hasFileAccess(String userId, String filePath, FileOperation operation) {
        // 实现您的权限检查逻辑
        switch (operation) {
            case READ:
                return hasReadPermission(userId, filePath);
            case WRITE:
                return hasWritePermission(userId, filePath);
            case DELETE:
                return hasDeletePermission(userId, filePath);
            default:
                return false;
        }
    }

    /**
     * 检查文件路径是否安全
     */
    public boolean isSecurePath(String filePath) {
        // 防止路径遍历攻击
        if (filePath.contains("../") || filePath.contains("..\\")) {
            return false;
        }

        // 检查是否访问系统敏感目录
        String[] forbiddenPaths = {"/etc/", "/sys/", "/proc/", "C:\\Windows\\System32\\"};
        for (String forbidden : forbiddenPaths) {
            if (filePath.toLowerCase().startsWith(forbidden.toLowerCase())) {
                return false;
            }
        }

        return true;
    }

    /**
     * 生成安全的文件名
     */
    public String sanitizeFileName(String fileName) {
        if (fileName == null) {
            return "unnamed_file";
        }

        // 移除危险字符
        String sanitized = fileName.replaceAll("[^a-zA-Z0-9._-]", "_");

        // 限制文件名长度
        if (sanitized.length() > 255) {
            String extension = "";
            int dotIndex = sanitized.lastIndexOf('.');
            if (dotIndex > 0) {
                extension = sanitized.substring(dotIndex);
                sanitized = sanitized.substring(0, 255 - extension.length()) + extension;
            } else {
                sanitized = sanitized.substring(0, 255);
            }
        }

        return sanitized;
    }

    private boolean hasReadPermission(String userId, String filePath) {
        // 实现读权限检查
        return true; // 示例实现
    }

    private boolean hasWritePermission(String userId, String filePath) {
        // 实现写权限检查
        return true; // 示例实现
    }

    private boolean hasDeletePermission(String userId, String filePath) {
        // 实现删除权限检查
        return true; // 示例实现
    }

    public enum FileOperation {
        READ, WRITE, DELETE
    }
}</code></pre>
                    </div>
                </div>

                <h2>安全最佳实践</h2>
                <div class="alert alert-warning">
                    <strong><i class="fas fa-exclamation-triangle"></i> 安全建议：</strong>
                    <ul>
                        <li><strong>使用HTTPS</strong>：在生产环境中始终使用HTTPS传输</li>
                        <li><strong>强密码策略</strong>：使用强密码和定期更换</li>
                        <li><strong>最小权限原则</strong>：只授予必要的文件访问权限</li>
                        <li><strong>定期备份</strong>：建立完善的备份和恢复机制</li>
                        <li><strong>监控日志</strong>：记录和监控所有文件操作</li>
                        <li><strong>网络隔离</strong>：将文件服务器部署在安全的网络环境中</li>
                    </ul>
                </div>

                <h2>SSL/TLS配置示例</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - SSL配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml"># HTTPS配置
server:
  port: 8443
  ssl:
    enabled: true
    key-store: classpath:keystore.p12
    key-store-password: ${SSL_KEYSTORE_PASSWORD}
    key-store-type: PKCS12
    key-alias: fileserver

# MinIO HTTPS配置
xcnf:
  data:
    fsclient:
      minio:
        endpoint: https://secure-minio.example.com
        access-key: ${MINIO_ACCESS_KEY}
        secret-key: ${MINIO_SECRET_KEY}</code></pre>
                    </div>
                </div>
            </section>

            <!-- API参考 -->
            <section id="api-reference" class="content-section">
                <h1><i class="fas fa-book"></i> API参考文档</h1>

                <p>本节提供了XCNF文件服务器客户端的完整API参考，包括核心接口、方法签名和使用示例。</p>

                <h2>核心接口</h2>

                <h3>FSClient 接口</h3>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FSClient.java - 核心文件操作接口</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">public interface FSClient&lt;T extends FileDesc&gt; extends Closeable {

    /**
     * 上传文件
     * @param fileDesc 文件描述信息
     * @param inputStream 文件输入流
     * @return 上传结果
     * @throws IOException IO异常
     */
    FileDescResult uploadFile(T fileDesc, InputStream inputStream) throws IOException;

    /**
     * 下载文件
     * @param fileDesc 文件描述信息
     * @param outputStream 文件输出流
     * @return 下载结果
     * @throws IOException IO异常
     */
    FileDescResult downloadFile(T fileDesc, OutputStream outputStream) throws IOException;

    /**
     * 删除文件
     * @param fileDesc 文件描述信息
     * @throws IOException IO异常
     */
    void deleteFile(T fileDesc) throws IOException;

    /**
     * 检查客户端是否打开
     * @return 是否打开
     */
    boolean isOpen();

    /**
     * 获取原始客户端对象
     * @return 原始客户端
     */
    Object getRawClient();
}</code></pre>
                    </div>
                </div>

                <h2>配置属性参考</h2>
                <table>
                    <tr>
                        <th>配置项</th>
                        <th>类型</th>
                        <th>默认值</th>
                        <th>说明</th>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.type</td>
                        <td>FSType</td>
                        <td>LOCAL</td>
                        <td>文件系统类型：LOCAL, FTP, SFTP, MINIO, FASTDFS, XFS</td>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.filter.file-type</td>
                        <td>String</td>
                        <td>空</td>
                        <td>允许的文件类型，逗号分隔</td>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.filter.max-upload-size</td>
                        <td>Long</td>
                        <td>-1</td>
                        <td>最大上传大小（字节），-1表示不限制</td>
                    </tr>
                    <tr>
                        <td>xcnf.data.fsclient.crypto.key</td>
                        <td>String</td>
                        <td>空</td>
                        <td>文件加密密钥</td>
                    </tr>
                </table>

                <h2>返回结果说明</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">FileDescResult - 操作结果</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">public class FileDescResult {
    private boolean success;        // 操作是否成功
    private String errorCode;       // 错误代码
    private String errorMessage;    // 错误消息
    private FileDesc fileDesc;      // 文件描述信息
    private Object data;            // 附加数据

    // 常见错误代码
    public static final String ERROR_FILE_NOT_FOUND = "FILE_NOT_FOUND";
    public static final String ERROR_ACCESS_DENIED = "ACCESS_DENIED";
    public static final String ERROR_DISK_FULL = "DISK_FULL";
    public static final String ERROR_NETWORK_TIMEOUT = "NETWORK_TIMEOUT";
    public static final String ERROR_INVALID_FILE_TYPE = "INVALID_FILE_TYPE";
    public static final String ERROR_FILE_TOO_LARGE = "FILE_TOO_LARGE";
}</code></pre>
                    </div>
                </div>
            </section>

            <!-- 安全配置 -->
            <section id="security" class="content-section">
                <h1><i class="fas fa-lock"></i> 安全配置与最佳实践</h1>

                <p>安全是文件服务的重要考虑因素。本节介绍如何配置和使用各种安全功能来保护您的文件系统。</p>

                <h2>文件过滤与验证</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">CustomSecurityInterceptor.java - 自定义安全拦截器</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Component
public class CustomSecurityInterceptor implements FileUploadInterceptor {

    private static final Logger logger = LoggerFactory.getLogger(CustomSecurityInterceptor.class);

    // 危险文件扩展名黑名单
    private static final List&lt;String&gt; DANGEROUS_EXTENSIONS = Arrays.asList(
        "exe", "bat", "cmd", "com", "pif", "scr", "vbs", "js", "jar", "sh", "php", "asp", "jsp"
    );

    @Override
    public FileDescResult preUpload(FileDesc fileDesc, InputStream inputStream) {
        try {
            // 1. 检查文件扩展名
            if (!isFileTypeAllowed(fileDesc.getFileName())) {
                return FileDescResult.builder()
                        .success(false)
                        .errorCode("DANGEROUS_FILE_TYPE")
                        .errorMessage("不允许上传此类型的文件")
                        .build();
            }

            // 2. 检查文件路径安全性
            if (!isPathSecure(fileDesc.getStoreName())) {
                return FileDescResult.builder()
                        .success(false)
                        .errorCode("UNSAFE_PATH")
                        .errorMessage("文件路径不安全")
                        .build();
            }

            // 3. 检查文件内容（病毒扫描等）
            if (!isContentSafe(inputStream)) {
                return FileDescResult.builder()
                        .success(false)
                        .errorCode("UNSAFE_CONTENT")
                        .errorMessage("文件内容不安全")
                        .build();
            }

            logger.info("文件安全检查通过: {}", fileDesc.getFileName());
            return FileDescResult.builder().success(true).build();

        } catch (Exception e) {
            logger.error("安全检查异常", e);
            return FileDescResult.builder()
                    .success(false)
                    .errorCode("SECURITY_CHECK_ERROR")
                    .errorMessage("安全检查失败")
                    .build();
        }
    }

    private boolean isFileTypeAllowed(String fileName) {
        if (fileName == null) return false;

        String extension = getFileExtension(fileName).toLowerCase();
        return !DANGEROUS_EXTENSIONS.contains(extension);
    }

    private boolean isPathSecure(String path) {
        if (path == null) return true;

        // 防止路径遍历攻击
        return !path.contains("../") && !path.contains("..\\") &&
               !path.toLowerCase().contains("system32") &&
               !path.toLowerCase().startsWith("/etc/");
    }

    private boolean isContentSafe(InputStream inputStream) {
        // 这里可以集成病毒扫描引擎
        // 示例：检查文件头部特征
        return true;
    }

    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }
}</code></pre>
                    </div>
                </div>

                <h2>加密配置</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - 加密配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">xcnf:
  data:
    fsclient:
      # 文件加密配置
      crypto:
        key: "${FILE_ENCRYPTION_KEY:change-this-key-in-production}"

# 环境变量方式（推荐）
# export FILE_ENCRYPTION_KEY="your-secure-encryption-key-here"</code></pre>
                    </div>
                </div>

                <h2>安全最佳实践</h2>
                <div class="alert alert-warning">
                    <strong><i class="fas fa-shield-alt"></i> 安全建议：</strong>
                    <ul>
                        <li><strong>使用HTTPS</strong>：在生产环境中始终使用HTTPS传输</li>
                        <li><strong>强密码策略</strong>：使用强密码和定期更换</li>
                        <li><strong>最小权限原则</strong>：只授予必要的文件访问权限</li>
                        <li><strong>定期备份</strong>：建立完善的备份和恢复机制</li>
                        <li><strong>监控日志</strong>：记录和监控所有文件操作</li>
                        <li><strong>网络隔离</strong>：将文件服务器部署在安全的网络环境中</li>
                        <li><strong>文件扫描</strong>：集成病毒扫描和恶意软件检测</li>
                        <li><strong>访问控制</strong>：实施基于角色的访问控制(RBAC)</li>
                    </ul>
                </div>
            </section>

            <!-- 故障排查 -->
            <section id="troubleshooting" class="content-section">
                <h1><i class="fas fa-tools"></i> 故障排查指南</h1>

                <p>本节提供常见问题的诊断和解决方案，帮助您快速定位和解决文件服务相关问题。</p>

                <h2>常见问题及解决方案</h2>

                <h3>1. 连接问题</h3>
                <div class="alert alert-danger">
                    <strong>问题：</strong>无法连接到文件服务器
                </div>

                <h4>可能原因和解决方案：</h4>
                <ul>
                    <li><strong>网络连通性</strong>：检查网络连接和防火墙设置</li>
                    <li><strong>服务器状态</strong>：确认文件服务器正在运行</li>
                    <li><strong>端口配置</strong>：验证端口号配置是否正确</li>
                    <li><strong>DNS解析</strong>：检查域名解析是否正常</li>
                </ul>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">连接测试命令</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-bash"># 测试网络连通性
ping your-server-host

# 测试端口连通性
telnet your-server-host 21    # FTP
telnet your-server-host 22    # SFTP
telnet your-server-host 9000  # MinIO

# 使用nc测试
nc -zv your-server-host 9000

# 测试HTTP连接
curl -I http://your-server-host:9000/minio/health/live</code></pre>
                    </div>
                </div>

                <h3>2. 认证问题</h3>
                <div class="alert alert-danger">
                    <strong>问题：</strong>认证失败，无法登录
                </div>

                <h4>解决步骤：</h4>
                <ol>
                    <li>检查用户名和密码是否正确</li>
                    <li>验证用户账户是否存在且未被锁定</li>
                    <li>检查权限配置</li>
                    <li>查看服务器日志获取详细错误信息</li>
                </ol>

                <h3>3. 文件上传失败</h3>
                <div class="alert alert-danger">
                    <strong>问题：</strong>文件上传失败或中断
                </div>

                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">诊断代码示例</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-java">@Service
public class FileUploadDiagnostic {

    public void diagnoseUploadFailure(MultipartFile file, String targetPath) {
        logger.info("开始诊断文件上传失败...");

        // 1. 检查文件基本信息
        logger.info("文件名: {}", file.getOriginalFilename());
        logger.info("文件大小: {} bytes", file.getSize());
        logger.info("内容类型: {}", file.getContentType());

        // 2. 检查文件是否为空
        if (file.isEmpty()) {
            logger.error("文件为空");
            return;
        }

        // 3. 检查文件大小限制
        long maxSize = 10 * 1024 * 1024; // 10MB
        if (file.getSize() > maxSize) {
            logger.error("文件大小超过限制: {} > {}", file.getSize(), maxSize);
            return;
        }

        // 4. 检查文件类型
        String extension = getFileExtension(file.getOriginalFilename());
        logger.info("文件扩展名: {}", extension);

        // 5. 检查目标路径
        logger.info("目标路径: {}", targetPath);

        // 6. 测试连接
        try {
            Object rawClient = fsTemplate.getRawClient();
            logger.info("文件服务器连接正常: {}", rawClient.getClass().getSimpleName());
        } catch (Exception e) {
            logger.error("文件服务器连接失败", e);
        }
    }
}</code></pre>
                    </div>
                </div>

                <h2>日志配置</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">logback-spring.xml - 日志配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-xml">&lt;configuration&gt;
    &lt;!-- 控制台输出 --&gt;
    &lt;appender name="CONSOLE" class="ch.qos.logback.core.ConsoleAppender"&gt;
        &lt;encoder&gt;
            &lt;pattern&gt;%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n&lt;/pattern&gt;
        &lt;/encoder&gt;
    &lt;/appender&gt;

    &lt;!-- 文件输出 --&gt;
    &lt;appender name="FILE" class="ch.qos.logback.core.rolling.RollingFileAppender"&gt;
        &lt;file&gt;logs/fileserver.log&lt;/file&gt;
        &lt;rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy"&gt;
            &lt;fileNamePattern&gt;logs/fileserver.%d{yyyy-MM-dd}.%i.log&lt;/fileNamePattern&gt;
            &lt;maxFileSize&gt;100MB&lt;/maxFileSize&gt;
            &lt;maxHistory&gt;30&lt;/maxHistory&gt;
        &lt;/rollingPolicy&gt;
        &lt;encoder&gt;
            &lt;pattern&gt;%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n&lt;/pattern&gt;
        &lt;/encoder&gt;
    &lt;/appender&gt;

    &lt;!-- 文件服务器相关日志 --&gt;
    &lt;logger name="com.xmcares.framework.fsclient" level="DEBUG" additivity="false"&gt;
        &lt;appender-ref ref="CONSOLE"/&gt;
        &lt;appender-ref ref="FILE"/&gt;
    &lt;/logger&gt;

    &lt;root level="INFO"&gt;
        &lt;appender-ref ref="CONSOLE"/&gt;
        &lt;appender-ref ref="FILE"/&gt;
    &lt;/root&gt;
&lt;/configuration&gt;</code></pre>
                    </div>
                </div>

                <h2>性能监控</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">application.yml - 监控配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml"># Spring Boot Actuator监控
management:
  endpoints:
    web:
      exposure:
        include: health,info,metrics,prometheus
  endpoint:
    health:
      show-details: always
  metrics:
    export:
      prometheus:
        enabled: true

# 自定义健康检查
health:
  fileserver:
    enabled: true</code></pre>
                    </div>
                </div>
            </section>

            <!-- 部署指南 -->
            <section id="deployment" class="content-section">
                <h1><i class="fas fa-rocket"></i> 部署指南</h1>

                <p>本节提供了在不同环境中部署文件服务器的完整指南，包括开发、测试和生产环境的最佳实践。</p>

                <h2>Docker部署</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">docker-compose.yml - 完整部署配置</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">version: '3.8'

services:
  # 应用服务
  fileserver-app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: fileserver-app
    ports:
      - "8080:8080"
    environment:
      SPRING_PROFILES_ACTIVE: docker
      XCNF_DATA_FSCLIENT_TYPE: minio
      XCNF_DATA_FSCLIENT_MINIO_ENDPOINT: http://minio:9000
      XCNF_DATA_FSCLIENT_MINIO_ACCESS_KEY: minioadmin
      XCNF_DATA_FSCLIENT_MINIO_SECRET_KEY: minioadmin123
      XCNF_DATA_FSCLIENT_MINIO_DEFAULT_BUCKET: fileserver
    depends_on:
      - minio
      - redis
    networks:
      - fileserver-network
    volumes:
      - ./logs:/app/logs
    restart: unless-stopped

  # MinIO对象存储
  minio:
    image: minio/minio:latest
    container_name: minio-server
    ports:
      - "9000:9000"
      - "9001:9001"
    environment:
      MINIO_ROOT_USER: minioadmin
      MINIO_ROOT_PASSWORD: minioadmin123
    volumes:
      - minio_data:/data
    command: server /data --console-address ":9001"
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:9000/minio/health/live"]
      interval: 30s
      timeout: 20s
      retries: 3
    networks:
      - fileserver-network
    restart: unless-stopped

  # Redis缓存
  redis:
    image: redis:7-alpine
    container_name: redis-cache
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - fileserver-network
    restart: unless-stopped

  # Nginx反向代理
  nginx:
    image: nginx:alpine
    container_name: nginx-proxy
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - ./nginx/logs:/var/log/nginx
    depends_on:
      - fileserver-app
    networks:
      - fileserver-network
    restart: unless-stopped

volumes:
  minio_data:
  redis_data:

networks:
  fileserver-network:
    driver: bridge</code></pre>
                    </div>
                </div>

                <h2>Kubernetes部署</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">k8s-deployment.yaml - Kubernetes部署</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                            <button class="btn-toggle" onclick="toggleCode(this)">折叠</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-yaml">apiVersion: apps/v1
kind: Deployment
metadata:
  name: fileserver-app
  labels:
    app: fileserver
spec:
  replicas: 3
  selector:
    matchLabels:
      app: fileserver
  template:
    metadata:
      labels:
        app: fileserver
    spec:
      containers:
      - name: fileserver
        image: your-registry/fileserver:latest
        ports:
        - containerPort: 8080
        env:
        - name: SPRING_PROFILES_ACTIVE
          value: "k8s"
        - name: XCNF_DATA_FSCLIENT_TYPE
          value: "minio"
        - name: XCNF_DATA_FSCLIENT_MINIO_ENDPOINT
          value: "http://minio-service:9000"
        - name: XCNF_DATA_FSCLIENT_MINIO_ACCESS_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: access-key
        - name: XCNF_DATA_FSCLIENT_MINIO_SECRET_KEY
          valueFrom:
            secretKeyRef:
              name: minio-secret
              key: secret-key
        resources:
          requests:
            memory: "512Mi"
            cpu: "250m"
          limits:
            memory: "1Gi"
            cpu: "500m"
        livenessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 60
          periodSeconds: 30
        readinessProbe:
          httpGet:
            path: /actuator/health
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 10

---
apiVersion: v1
kind: Service
metadata:
  name: fileserver-service
spec:
  selector:
    app: fileserver
  ports:
  - protocol: TCP
    port: 80
    targetPort: 8080
  type: LoadBalancer

---
apiVersion: v1
kind: Secret
metadata:
  name: minio-secret
type: Opaque
data:
  access-key: bWluaW9hZG1pbg==  # base64 encoded
  secret-key: bWluaW9hZG1pbjEyMw==  # base64 encoded</code></pre>
                    </div>
                </div>

                <h2>生产环境配置清单</h2>
                <div class="alert alert-info">
                    <strong><i class="fas fa-check-circle"></i> 部署检查清单：</strong>
                    <ul>
                        <li><strong>✓ 环境变量</strong>：配置所有必要的环境变量</li>
                        <li><strong>✓ 数据库连接</strong>：确保数据库连接配置正确</li>
                        <li><strong>✓ 文件存储</strong>：配置文件存储后端</li>
                        <li><strong>✓ 日志配置</strong>：设置适当的日志级别和输出</li>
                        <li><strong>✓ 监控配置</strong>：启用健康检查和指标收集</li>
                        <li><strong>✓ 安全配置</strong>：配置HTTPS和访问控制</li>
                        <li><strong>✓ 备份策略</strong>：建立数据备份和恢复机制</li>
                        <li><strong>✓ 负载均衡</strong>：配置负载均衡器</li>
                        <li><strong>✓ 缓存配置</strong>：配置Redis或其他缓存</li>
                        <li><strong>✓ 性能调优</strong>：调整JVM参数和连接池</li>
                    </ul>
                </div>

                <h2>性能优化建议</h2>
                <div class="code-container">
                    <div class="code-header">
                        <span class="code-title">JVM优化参数</span>
                        <div class="code-actions">
                            <button class="btn-copy" onclick="copyCode(this)">复制</button>
                        </div>
                    </div>
                    <div class="code-content">
                        <pre><code class="language-bash"># JVM参数优化
JAVA_OPTS="-Xms1g -Xmx2g \
           -XX:+UseG1GC \
           -XX:MaxGCPauseMillis=200 \
           -XX:+HeapDumpOnOutOfMemoryError \
           -XX:HeapDumpPath=/app/logs/heapdump.hprof \
           -Djava.security.egd=file:/dev/./urandom"

# 启动命令
java $JAVA_OPTS -jar fileserver.jar</code></pre>
                    </div>
                </div>

                <div class="alert alert-success">
                    <strong><i class="fas fa-lightbulb"></i> 恭喜！</strong>
                    您已经完成了XCNF文件服务器客户端的学习。现在您可以：
                    <ul>
                        <li>在项目中集成和使用文件服务器</li>
                        <li>配置不同类型的存储后端</li>
                        <li>实施安全最佳实践</li>
                        <li>部署到生产环境</li>
                        <li>进行故障排查和性能优化</li>
                    </ul>
                    如有问题，请参考相关章节或联系技术支持。
                </div>
            </section>
        </main>

        <!-- 页脚 -->
        <footer style="background: #2c3e50; color: white; padding: 30px; margin-top: 50px; margin-left: 280px;">
            <div style="max-width: 1200px;">
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 30px; margin-bottom: 20px;">
                    <div>
                        <h4 style="margin-bottom: 15px; color: #3498db;">
                            <i class="fas fa-server"></i> XCNF文件服务器
                        </h4>
                        <p style="line-height: 1.6; opacity: 0.9;">
                            统一文件存储客户端框架，支持多种存储后端，提供企业级文件管理解决方案。
                        </p>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 15px; color: #3498db;">
                            <i class="fas fa-link"></i> 快速链接
                        </h4>
                        <ul style="list-style: none; padding: 0;">
                            <li style="margin-bottom: 8px;">
                                <a href="#overview" onclick="showSection('overview')" style="color: #ecf0f1; text-decoration: none;">
                                    <i class="fas fa-home"></i> 项目概述
                                </a>
                            </li>
                            <li style="margin-bottom: 8px;">
                                <a href="#spring-integration" onclick="showSection('spring-integration')" style="color: #ecf0f1; text-decoration: none;">
                                    <i class="fas fa-leaf"></i> Spring Boot集成
                                </a>
                            </li>
                            <li style="margin-bottom: 8px;">
                                <a href="#api-reference" onclick="showSection('api-reference')" style="color: #ecf0f1; text-decoration: none;">
                                    <i class="fas fa-book"></i> API参考
                                </a>
                            </li>
                            <li style="margin-bottom: 8px;">
                                <a href="#deployment" onclick="showSection('deployment')" style="color: #ecf0f1; text-decoration: none;">
                                    <i class="fas fa-rocket"></i> 部署指南
                                </a>
                            </li>
                        </ul>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 15px; color: #3498db;">
                            <i class="fas fa-database"></i> 支持的存储
                        </h4>
                        <div style="display: flex; flex-wrap: wrap; gap: 10px;">
                            <span style="background: #34495e; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;">LOCAL</span>
                            <span style="background: #34495e; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;">FTP</span>
                            <span style="background: #34495e; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;">SFTP</span>
                            <span style="background: #34495e; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;">MinIO</span>
                            <span style="background: #34495e; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;">FastDFS</span>
                            <span style="background: #34495e; padding: 4px 8px; border-radius: 4px; font-size: 0.9em;">WebService</span>
                        </div>
                    </div>
                    <div>
                        <h4 style="margin-bottom: 15px; color: #3498db;">
                            <i class="fas fa-info-circle"></i> 文档信息
                        </h4>
                        <p style="line-height: 1.6; opacity: 0.9; font-size: 0.9em;">
                            <strong>版本：</strong> 1.4.6.1-SNAPSHOT<br>
                            <strong>更新时间：</strong> 2025年1月<br>
                            <strong>文档格式：</strong> HTML交互式文档<br>
                            <strong>支持功能：</strong> 搜索、代码复制、响应式设计
                        </p>
                    </div>
                </div>

                <hr style="border: none; border-top: 1px solid #34495e; margin: 20px 0;">

                <div style="display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap; gap: 15px;">
                    <div style="opacity: 0.8;">
                        <p style="margin: 0;">
                            © 2025 厦门民航凯亚有限公司. 保留所有权利.
                        </p>
                    </div>
                    <div style="display: flex; gap: 15px;">
                        <button onclick="window.print()" style="background: #3498db; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-print"></i> 打印文档
                        </button>
                        <button onclick="exportToPDF()" style="background: #e74c3c; color: white; border: none; padding: 8px 15px; border-radius: 4px; cursor: pointer;">
                            <i class="fas fa-file-pdf"></i> 导出PDF
                        </button>
                    </div>
                </div>
            </div>
        </footer>
    </div>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop" onclick="scrollToTop()">
        <i class="fas fa-arrow-up"></i>
    </button>

    <!-- JavaScript -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/components/prism-core.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/prism/1.29.0/plugins/autoloader/prism-autoloader.min.js"></script>
    <script>
        // 当前活动的章节
        let currentSection = 'overview';

        // 显示指定章节
        function showSection(sectionId) {
            // 隐藏所有章节
            document.querySelectorAll('.content-section').forEach(section => {
                section.classList.remove('active');
            });

            // 显示指定章节
            const targetSection = document.getElementById(sectionId);
            if (targetSection) {
                targetSection.classList.add('active');
                currentSection = sectionId;
            }

            // 更新导航状态
            document.querySelectorAll('.nav-item').forEach(item => {
                item.classList.remove('active');
            });

            const activeNavItem = document.querySelector(`[onclick="showSection('${sectionId}')"]`);
            if (activeNavItem) {
                activeNavItem.classList.add('active');
            }

            // 滚动到顶部
            window.scrollTo(0, 0);
        }

        // 切换侧边栏（移动端）
        function toggleSidebar() {
            const sidebar = document.getElementById('sidebar');
            sidebar.classList.toggle('open');
        }

        // 复制代码功能
        function copyCode(button) {
            const codeContainer = button.closest('.code-container');
            const codeElement = codeContainer.querySelector('pre code');
            const text = codeElement.textContent;

            navigator.clipboard.writeText(text).then(() => {
                const originalText = button.textContent;
                button.textContent = '已复制';
                button.style.background = '#28a745';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#4a5568';
                }, 2000);
            }).catch(err => {
                console.error('复制失败:', err);
                // 降级方案
                const textArea = document.createElement('textarea');
                textArea.value = text;
                document.body.appendChild(textArea);
                textArea.select();
                document.execCommand('copy');
                document.body.removeChild(textArea);

                button.textContent = '已复制';
                setTimeout(() => {
                    button.textContent = '复制';
                }, 2000);
            });
        }

        // 切换代码折叠
        function toggleCode(button) {
            const codeContainer = button.closest('.code-container');
            const codeContent = codeContainer.querySelector('.code-content');

            if (codeContent.classList.contains('collapsed')) {
                codeContent.classList.remove('collapsed');
                button.textContent = '折叠';
            } else {
                codeContent.classList.add('collapsed');
                button.textContent = '展开';
            }
        }

        // 搜索功能
        function initSearch() {
            const searchInput = document.getElementById('searchInput');
            let searchTimeout;

            searchInput.addEventListener('input', function() {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    const query = this.value.toLowerCase().trim();
                    if (query) {
                        performSearch(query);
                    } else {
                        clearSearchHighlight();
                    }
                }, 300);
            });
        }

        function performSearch(query) {
            clearSearchHighlight();

            const sections = document.querySelectorAll('.content-section');
            let found = false;

            sections.forEach(section => {
                const content = section.textContent.toLowerCase();
                if (content.includes(query)) {
                    if (!found) {
                        // 显示第一个匹配的章节
                        const sectionId = section.id;
                        showSection(sectionId);
                        found = true;
                    }

                    // 高亮匹配的文本
                    highlightText(section, query);
                }
            });

            if (!found) {
                alert('未找到匹配的内容');
            }
        }

        function highlightText(element, query) {
            const walker = document.createTreeWalker(
                element,
                NodeFilter.SHOW_TEXT,
                null,
                false
            );

            const textNodes = [];
            let node;

            while (node = walker.nextNode()) {
                if (node.textContent.toLowerCase().includes(query)) {
                    textNodes.push(node);
                }
            }

            textNodes.forEach(textNode => {
                const parent = textNode.parentNode;
                const text = textNode.textContent;
                const regex = new RegExp(`(${query})`, 'gi');
                const highlightedText = text.replace(regex, '<mark>$1</mark>');

                const wrapper = document.createElement('span');
                wrapper.innerHTML = highlightedText;
                parent.replaceChild(wrapper, textNode);
            });
        }

        function clearSearchHighlight() {
            document.querySelectorAll('mark').forEach(mark => {
                const parent = mark.parentNode;
                parent.replaceChild(document.createTextNode(mark.textContent), mark);
                parent.normalize();
            });
        }

        // 返回顶部功能
        function scrollToTop() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }

        // 监听滚动事件，显示/隐藏返回顶部按钮
        window.addEventListener('scroll', function() {
            const backToTop = document.getElementById('backToTop');
            if (window.pageYOffset > 300) {
                backToTop.style.display = 'block';
            } else {
                backToTop.style.display = 'none';
            }
        });

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            initSearch();

            // 初始化代码块折叠状态
            document.querySelectorAll('.code-content').forEach(codeContent => {
                const lines = codeContent.textContent.split('\n').length;
                if (lines > 20) {
                    // 超过20行的代码块默认折叠
                    codeContent.classList.add('collapsed');
                    const toggleBtn = codeContent.closest('.code-container').querySelector('.btn-toggle');
                    if (toggleBtn) {
                        toggleBtn.textContent = '展开';
                    }
                }
            });

            // 移动端点击外部关闭侧边栏
            document.addEventListener('click', function(e) {
                const sidebar = document.getElementById('sidebar');
                const mobileMenuBtn = document.querySelector('.mobile-menu-btn');

                if (window.innerWidth <= 768 &&
                    !sidebar.contains(e.target) &&
                    !mobileMenuBtn.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });

            // 初始化代码高亮
            if (typeof Prism !== 'undefined') {
                Prism.highlightAll();
            }

            // 添加键盘快捷键支持
            document.addEventListener('keydown', function(e) {
                // Ctrl+F 或 Cmd+F 聚焦搜索框
                if ((e.ctrlKey || e.metaKey) && e.key === 'f') {
                    e.preventDefault();
                    document.getElementById('searchInput').focus();
                }

                // ESC 清除搜索
                if (e.key === 'Escape') {
                    const searchInput = document.getElementById('searchInput');
                    if (searchInput.value) {
                        searchInput.value = '';
                        clearSearchHighlight();
                    }
                }
            });

            // 添加平滑滚动
            document.querySelectorAll('a[href^="#"]').forEach(anchor => {
                anchor.addEventListener('click', function (e) {
                    e.preventDefault();
                    const target = document.querySelector(this.getAttribute('href'));
                    if (target) {
                        target.scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }
                });
            });

            // 添加复制成功提示
            const style = document.createElement('style');
            style.textContent = `
                .copy-tooltip {
                    position: fixed;
                    background: #28a745;
                    color: white;
                    padding: 8px 12px;
                    border-radius: 4px;
                    font-size: 14px;
                    z-index: 10000;
                    pointer-events: none;
                    opacity: 0;
                    transition: opacity 0.3s;
                }
                .copy-tooltip.show {
                    opacity: 1;
                }
            `;
            document.head.appendChild(style);
        });

        // 显示复制提示
        function showCopyTooltip(button, message = '已复制到剪贴板') {
            const tooltip = document.createElement('div');
            tooltip.className = 'copy-tooltip';
            tooltip.textContent = message;

            const rect = button.getBoundingClientRect();
            tooltip.style.left = rect.left + 'px';
            tooltip.style.top = (rect.top - 40) + 'px';

            document.body.appendChild(tooltip);

            // 显示动画
            setTimeout(() => tooltip.classList.add('show'), 10);

            // 隐藏并移除
            setTimeout(() => {
                tooltip.classList.remove('show');
                setTimeout(() => document.body.removeChild(tooltip), 300);
            }, 2000);
        }

        // 增强的复制功能
        function copyCode(button) {
            const codeContainer = button.closest('.code-container');
            const codeElement = codeContainer.querySelector('pre code');
            const text = codeElement.textContent;

            // 尝试使用现代API
            if (navigator.clipboard && window.isSecureContext) {
                navigator.clipboard.writeText(text).then(() => {
                    showCopyTooltip(button);
                    button.style.background = '#28a745';
                    setTimeout(() => {
                        button.style.background = '#4a5568';
                    }, 1000);
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopy(text, button);
                });
            } else {
                fallbackCopy(text, button);
            }
        }

        // 降级复制方案
        function fallbackCopy(text, button) {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            try {
                document.execCommand('copy');
                showCopyTooltip(button);
                button.style.background = '#28a745';
                setTimeout(() => {
                    button.style.background = '#4a5568';
                }, 1000);
            } catch (err) {
                console.error('降级复制也失败:', err);
                showCopyTooltip(button, '复制失败，请手动复制');
            }

            document.body.removeChild(textArea);
        }

        // 增强的搜索功能
        function performSearch(query) {
            clearSearchHighlight();

            const sections = document.querySelectorAll('.content-section');
            const results = [];

            sections.forEach(section => {
                const content = section.textContent.toLowerCase();
                if (content.includes(query)) {
                    const sectionTitle = section.querySelector('h1').textContent;
                    const matches = (content.match(new RegExp(query, 'gi')) || []).length;
                    results.push({
                        section: section,
                        sectionId: section.id,
                        title: sectionTitle,
                        matches: matches
                    });
                }
            });

            if (results.length > 0) {
                // 按匹配数量排序
                results.sort((a, b) => b.matches - a.matches);

                // 显示最相关的章节
                showSection(results[0].sectionId);

                // 高亮所有匹配的文本
                results.forEach(result => {
                    highlightText(result.section, query);
                });

                // 滚动到第一个匹配项
                const firstMatch = document.querySelector('mark');
                if (firstMatch) {
                    firstMatch.scrollIntoView({
                        behavior: 'smooth',
                        block: 'center'
                    });
                }
            } else {
                // 创建无结果提示
                const noResultsDiv = document.createElement('div');
                noResultsDiv.className = 'alert alert-warning';
                noResultsDiv.innerHTML = `
                    <strong><i class="fas fa-search"></i> 搜索结果</strong><br>
                    未找到包含 "${query}" 的内容。请尝试其他关键词。
                `;

                // 显示在当前章节顶部
                const currentSection = document.querySelector('.content-section.active');
                if (currentSection) {
                    currentSection.insertBefore(noResultsDiv, currentSection.firstChild);
                    setTimeout(() => {
                        if (noResultsDiv.parentNode) {
                            noResultsDiv.parentNode.removeChild(noResultsDiv);
                        }
                    }, 3000);
                }
            }
        }

        // PDF导出功能
        function exportToPDF() {
            // 简单的PDF导出提示
            alert('PDF导出功能需要集成专门的PDF生成库。\n\n建议使用浏览器的打印功能：\n1. 按 Ctrl+P (Windows) 或 Cmd+P (Mac)\n2. 选择"另存为PDF"\n3. 调整页面设置后保存');
        }

        // 移动端适配
        function handleMobileResize() {
            const sidebar = document.getElementById('sidebar');
            const footer = document.querySelector('footer');

            if (window.innerWidth <= 768) {
                // 移动端：调整页脚边距
                if (footer) {
                    footer.style.marginLeft = '0';
                }
            } else {
                // 桌面端：恢复页脚边距
                if (footer) {
                    footer.style.marginLeft = '280px';
                }
                // 关闭移动端侧边栏
                sidebar.classList.remove('open');
            }
        }

        // 监听窗口大小变化
        window.addEventListener('resize', handleMobileResize);

        // 添加全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e.error);
        });

        // 添加性能监控
        window.addEventListener('load', function() {
            if ('performance' in window) {
                const loadTime = performance.timing.loadEventEnd - performance.timing.navigationStart;
                console.log('页面加载时间:', loadTime + 'ms');
            }
        });

        // 添加键盘导航支持
        document.addEventListener('keydown', function(e) {
            // Alt + 数字键快速导航到章节
            if (e.altKey && e.key >= '1' && e.key <= '9') {
                e.preventDefault();
                const sections = ['overview', 'features', 'requirements', 'storage-types',
                                'local-config', 'ftp-config', 'sftp-config', 'minio-config', 'fastdfs-config'];
                const index = parseInt(e.key) - 1;
                if (sections[index]) {
                    showSection(sections[index]);
                }
            }
        });

        // 添加主题切换功能（可选）
        function toggleTheme() {
            const body = document.body;
            const isDark = body.classList.contains('dark-theme');

            if (isDark) {
                body.classList.remove('dark-theme');
                localStorage.setItem('theme', 'light');
            } else {
                body.classList.add('dark-theme');
                localStorage.setItem('theme', 'dark');
            }
        }

        // 恢复主题设置
        function restoreTheme() {
            const savedTheme = localStorage.getItem('theme');
            if (savedTheme === 'dark') {
                document.body.classList.add('dark-theme');
            }
        }

        // 添加进度指示器
        function updateProgressIndicator() {
            const sections = document.querySelectorAll('.content-section');
            const currentSectionIndex = Array.from(sections).findIndex(section =>
                section.classList.contains('active'));

            if (currentSectionIndex >= 0) {
                const progress = ((currentSectionIndex + 1) / sections.length) * 100;
                // 可以在这里更新进度条UI
                console.log('阅读进度:', Math.round(progress) + '%');
            }
        }

        // 增强showSection函数
        const originalShowSection = showSection;
        showSection = function(sectionId) {
            originalShowSection(sectionId);
            updateProgressIndicator();

            // 更新URL hash（不触发页面跳转）
            if (history.replaceState) {
                history.replaceState(null, null, '#' + sectionId);
            }
        };

        // 页面加载时检查URL hash
        window.addEventListener('load', function() {
            const hash = window.location.hash.substring(1);
            if (hash && document.getElementById(hash)) {
                showSection(hash);
            }

            // 恢复主题
            restoreTheme();

            // 初始化移动端适配
            handleMobileResize();
        });
    </script>
</body>
</html>
