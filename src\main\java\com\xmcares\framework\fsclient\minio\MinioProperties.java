/*
 * 厦门民航凯亚有限公司，版权所有，违者必究
 *
 * Copyright (c) 2020
 * Author： eliming
 * Date：2020/9/13
 */
package com.xmcares.framework.fsclient.minio;

import org.springframework.util.StringUtils;

/**
 * FTP服务器配置
 * <AUTHOR>
 * @since 1.0.0
 */
public class MinioProperties {
    /**
     * MinIO endpoint
     */
    private String endpoint;

    private String accessKey;

    private String secretKey;

    /**
     * MinIO 默认使用的bucket
     */
    private String defaultBucket;


    public String getEndpoint() {
        return endpoint;
    }

    public void setEndpoint(String endpoint) {
        this.endpoint = endpoint;
    }

    public String getAccessKey() {
        return accessKey;
    }

    public void setAccessKey(String accessKey) {
        this.accessKey = accessKey;
    }

    public String getSecretKey() {
        return secretKey;
    }

    public void setSecretKey(String secretKey) {
        this.secretKey = secretKey;
    }

    public String getDefaultBucket() {
        return defaultBucket;
    }

    public void setDefaultBucket(String defaultBucket) {
        this.defaultBucket = defaultBucket;
    }

    public void setDefaultBucketIfAbsent(String defaultBucket) {
        if (!StringUtils.hasText(this.defaultBucket)) {
            this.setDefaultBucket(defaultBucket);
        }
    }
}
